<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Profil - Aphrovision</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', sans-serif;
    }

    body {
      background-color: #f7f9fb;
      color: #333;
    }

    /* Navigation */
    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: bold;
      color: black;
    }

    .nav-links a {
      margin: 0 12px;
      color: #333;
      text-decoration: none;
      font-weight: 500;
    }

    .nav-links a:hover {
      color: #ff4d4d;
    }

    /* Profile Section */
    .profile-section {
      display: flex;
      justify-content: space-between;
      padding: 50px;
      flex-wrap: wrap;
    }

    .profile-info {
      flex: 1;
      max-width: 600px;
    }

    .profile-info h1 {
      font-size: 2.2em;
      margin-bottom: 5px;
    }

    .handle {
      color: #777;
      font-size: 1em;
      margin-bottom: 15px;
    }

    .bio {
      font-size: 1.1em;
      margin-bottom: 30px;
    }

    .profile-info h3 {
      margin-top: 20px;
      margin-bottom: 10px;
      font-size: 1.2em;
    }

    .profile-info p {
      margin: 5px 0;
      font-size: 1em;
    }

    .add-button {
      background-color: #ffffff;
      color: rgb(8, 8, 8);
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 30px;
      font-size: 1em;
    }

    .add-button:hover {
      background-color:#629208; ;
    }

    /* Photo & Stats */
    .profile-photo-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .follow-stats {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin-bottom: 15px;
      font-size: 1em;
      color: #555;
    }

    .profile-photo {
      width: 350px;
      height: 350px;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #ccc;
    }

    .profile-photo img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    @media (max-width: 768px) {
      .profile-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .profile-info {
        max-width: 100%;
      }

      .profile-photo {
        width: 300px;
        height: 300px;
      }

      .follow-stats {
        gap: 20px;
      }
    }
  </style>
</head>
<body>

  <!-- Navigation -->
  <div class="navbar">
    <div class="logo">Aphrovision</div>
    <div class="nav-links">
      <a href="explore.html">EXPLORER</a>
      <a href="profile_view.html">PROFIL</a>
      <a href="photographers.html">PHOTOGRAPHES</a>
      <a href="poster.html">POSTER</a>
      <a href="#">MESSAGERIE</a>
      <a href="shop.html">MAGASIN</a>
    </div>
      <div></div> 
  </div>

  <!-- Profile Section -->
  <div class="profile-section">

    <!-- Left Info -->
    <div class="profile-info">
      <button class="add-button">Reserver</button>
      <button class="add-button">Message</button>
      


      <h1>Hallali Tounsia</h1>
      <div class="handle">@tounsia</div>
      <div class="bio">Photographer from Algeria.</div>

      <h3>Informations générale</h3>
      <p><strong>Address :</strong> 16000 Alger</p>
      <p><strong>E-mail :</strong> <EMAIL></p>
      <p><strong>Phone :</strong> 0350157952</p>
      <p><strong>Appareil :</strong> Canon EOS 700d</p>
    </div>

    <!-- Photo + Stats -->
    <div class="profile-photo-wrapper">
      <div class="follow-stats">
        <div><strong>1</strong> Abonnées</div>
        <div><strong>3</strong> Abonnements</div>
      </div>
      <div class="profile-photo">
        <img src="photos3.jpg" alt="Hallali Tounsia" />
      </div>
    </div>

  </div>
 <script>
  // Références
  const buttons = document.querySelectorAll(".add-button"); // [0] = Réserver, [1] = Message
  const messageModal = document.getElementById("messageModal");
  const closeModal = document.querySelector(".close");
  const sendMessage = document.getElementById("sendMessage");

  // Bouton "Réserver"
  buttons[0].addEventListener("click", () => {
    const confirmation = confirm("Voulez-vous envoyer une demande de réservation à Tounsia ?");
    if (confirmation) {
      alert("Demande de réservation envoyée !");
    }
  });

  // Bouton "Message" => ouvrir la modale
  buttons[1].addEventListener("click", () => {
    const confirmation = confirm("Voulez-vous envoyer un message à Tounsia ?");
   if (confirmation) {
  const reservationMessage = prompt("Écrivez votre message pour la demande de réservation :");
  if (reservationMessage && reservationMessage.trim() !== "") {
    alert("Demande de réservation envoyée avec ce message :\n\n" + reservationMessage);
    // Ici tu peux ajouter l’envoi vers le serveur si besoin
  } else {
    alert("Message de réservation vide, demande annulée.");
  }
}

  });
  // Bouton "Envoyer" dans la modale
  sendMessage.addEventListener("click", () => {
    const message = document.getElementById("messageText").value.trim();
    if (message) {
      alert("Message envoyé :\n\n" + message);
      document.getElementById("messageText").value = "";
      messageModal.style.display = "none";
    } else {
      alert("Veuillez écrire un message.");
    }
  });

  // Fermer la modale
  closeModal.addEventListener("click", () => {
    messageModal.style.display = "none";
  });

  // Cliquer en dehors de la modale pour la fermer
  window.addEventListener("click", (e) => {
    if (e.target === messageModal) {
      messageModal.style.display = "none";
    }
  });
</script>


</body>
</html>  