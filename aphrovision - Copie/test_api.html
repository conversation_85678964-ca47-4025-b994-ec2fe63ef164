<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API - Aphrovision</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .photographer {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .details {
            margin-top: 5px;
            color: #666;
        }
        .bio {
            margin-top: 10px;
            font-style: italic;
            color: #555;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test API Photographes</h1>
        
        <div>
            <button onclick="loadAll()">Charger Tous les Photographes</button>
            <button onclick="loadPortrait()">Photographes Portrait</button>
            <button onclick="loadMariage()">Photographes Mariage</button>
            <button onclick="loadAlger()">Photographes d'Alger</button>
        </div>
        
        <div id="status"></div>
        <div id="results"></div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        function displayPhotographers(photographers) {
            const resultsDiv = document.getElementById('results');
            
            if (photographers.length === 0) {
                resultsDiv.innerHTML = '<p>Aucun photographe trouvé.</p>';
                return;
            }

            let html = `<h2>📸 ${photographers.length} Photographe(s) trouvé(s)</h2>`;
            
            photographers.forEach(p => {
                html += `
                    <div class="photographer">
                        <div class="name">👤 ${p.nom} ${p.prenom}</div>
                        <div class="details">
                            🎯 <strong>Domaine:</strong> ${p.domaine} | 
                            📍 <strong>Wilaya:</strong> ${p.wilaya}
                        </div>
                        <div class="details">
                            📧 ${p.email} | 📱 ${p.phone}
                        </div>
                        <div class="bio">💬 ${p.bio}</div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        async function loadPhotographers(domaine = '', wilaya = '') {
            try {
                showStatus('🔄 Chargement en cours...');
                
                const params = new URLSearchParams();
                if (domaine) params.append('domaine', domaine);
                if (wilaya) params.append('wilaya', wilaya);

                const url = `/api/photographers${params.toString() ? '?' + params.toString() : ''}`;
                console.log('Fetching:', url);
                
                const response = await fetch(url);

                if (response.ok) {
                    const photographers = await response.json();
                    console.log('Response:', photographers);
                    
                    showStatus(`✅ Données chargées avec succès! (${photographers.length} photographes)`);
                    displayPhotographers(photographers);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('Erreur:', error);
                showStatus(`❌ Erreur: ${error.message}`, true);
                document.getElementById('results').innerHTML = '';
            }
        }

        function loadAll() {
            loadPhotographers();
        }

        function loadPortrait() {
            loadPhotographers('Portrait');
        }

        function loadMariage() {
            loadPhotographers('Mariage');
        }

        function loadAlger() {
            loadPhotographers('', 'Alger');
        }

        // Chargement automatique au démarrage
        window.onload = function() {
            showStatus('🚀 Page de test chargée. Cliquez sur un bouton pour tester l\'API.');
        };
    </script>
</body>
</html>
