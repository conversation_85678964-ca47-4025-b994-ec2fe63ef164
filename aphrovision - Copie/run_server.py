#!/usr/bin/env python3
"""
Aphrovision Server Startup Script
Run this file to start the Flask development server
"""

import os
import sys
from app import app

if __name__ == '__main__':
    print("🚀 Starting Aphrovision Server...")
    print("📍 Server will be available at: http://localhost:5000")
    print("📁 Static files served from: static/")
    print("🗄️  Database: photographe_frelance.db")
    print("=" * 50)
    
    # Ensure upload directory exists
    upload_dir = os.path.join(os.path.dirname(__file__), 'static', 'uploads')
    os.makedirs(upload_dir, exist_ok=True)
    
    try:
        app.run(
            host='0.0.0.0',  # Allow external connections
            port=5000,
            debug=True,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)
