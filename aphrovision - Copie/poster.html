<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Aphrovision - Poster une Image</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', sans-serif;
    }

    body {
      background-color: #f5f5f5;
      color: #333;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* Navigation */
    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
      flex-shrink: 0;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: bold;
      user-select: none;
    }

    .nav-links a {
      margin: 0 10px;
      color: #333;
      text-decoration: none;
      font-weight: 500;
      user-select: none;
    }

    .nav-links a:hover {
      color: #629208;
    }

    .notif-icon {
      font-size: 1.3em;
      cursor: pointer;
      user-select: none;
    }

    /* Main container with sidebars and content */
    .main {
      flex: 1;
      display: flex;
      gap: 15px;
      padding: 15px 40px;
      overflow: hidden;
    }

    /* Left Sidebar */
    .left-sidebar {
      width: 220px;
      background-color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      user-select: none;
    }

    .discover-box p {
      margin-bottom: 10px;
      font-weight: 600;
      text-align: center;
    }

    .discover-box img {
      width: 100%;
      border-radius: 5px;
      object-fit: cover;
    }

    /* Center content area (upload) */
    .content-wrapper {
      flex: 1;
      background-color: white;
      border-radius: 12px;
      padding: 30px 40px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      display: flex;
      flex-direction: column;
      align-items: center;
      overflow-y: auto;
      user-select: none;
    }

    header {
      width: 100%;
      text-align: center;
      margin-bottom: 30px;
    }

    header h1 {
      font-family: 'Playfair Display', serif;
      font-size: 2.2em;
      font-weight: bold;
      color: #222;
      display: flex;
      justify-content: center;
      gap: 10px;
      align-items: center;
    }

    .upload-zone {
      border: 2px dashed #333;
      border-radius: 15px;
      width: 100%;
      max-width: 600px;
      height: 350px;
      background-color: rgba(50, 50, 50, 0.1);
      color: #555;
      font-weight: 600;
      font-size: 1.2em;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      padding: 20px;
      cursor: pointer;
      position: relative;
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .upload-zone.dragover {
      background-color: #62920822;
      border-color: #629208;
      color: #629208;
    }

    .upload-zone input[type="file"] {
      display: none;
    }

    .image-preview {
      margin-top: 25px;
      max-width: 600px;
      max-height: 350px;
      border-radius: 15px;
      object-fit: contain;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
      display: none;
    }

    .actions {
      margin-top: 30px;
      display: flex;
      gap: 20px;
      justify-content: center;
      flex-wrap: wrap;
      width: 100%;
      max-width: 600px;
    }

    .actions button {
      background: white;
      color: #333;
      border: 1.5px solid #ddd;
      padding: 12px 30px;
      border-radius: 6px;
      font-size: 1.1em;
      cursor: pointer;
      transition: all 0.3s ease;
      flex: 1 1 150px;
      max-width: 200px;
    }

    .actions button:hover {
      background-color: #629208;
      color: white;
      border-color: #629208;
    }

    /* Right Sidebar */
    .right-sidebar {
      width: 220px;
      background-color: white;
      padding: 15px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      user-select: none;
      overflow-y: auto;
    }

    .suggestions h4 {
      margin-bottom: 10px;
    }

    .suggestion-user {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .suggestion-user img {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      margin-right: 10px;
      object-fit: cover;
    }

    /* Responsive */
    @media screen and (max-width: 900px) {
      .main {
        flex-direction: column;
        padding: 15px 20px;
      }

      .left-sidebar,
      .right-sidebar {
        width: 100%;
        margin-bottom: 20px;
      }

      .content-wrapper {
        max-width: 100%;
      }

      .actions {
        max-width: 100%;
      }
    }
  </style>
</head>
<body>

  <!-- Navigation -->
  <nav class="navbar" role="navigation" aria-label="Menu principal">
    <div class="logo" tabindex="0">Aphrovision</div>
    <div class="nav-links" role="menubar">
      <a href="explore.html" tabindex="0" role="menuitem">EXPLORER</a>
      <a href="profile_view.html" tabindex="0" role="menuitem">PROFIL</a>
      <a href="photographers.html" tabindex="0" role="menuitem">PHOTOGRAPHES</a>
      <a href="poster.html" tabindex="0" role="menuitem">POSTER</a>
      <a href="#" tabindex="0" role="menuitem">MESSAGERIE</a>
      <a href="shop.html" tabindex="0" role="menuitem">MAGASIN</a>
    </div>
    <div class="notif-icon" aria-label="Notifications" role="button" tabindex="0">🔔</div>
  </nav>

  <!-- Main content area with sidebars -->
  <div class="main">

    <!-- Center content - upload image -->
    <main class="content-wrapper" role="main">
      <header>
        <h1>📤 Poster une Image</h1>
      </header>

      <form id="uploadForm" enctype="multipart/form-data">
        <label class="upload-zone" id="uploadZone" for="fileInput">
          Déposez votre image ici ou cliquez pour parcourir...
          <input type="file" accept="image/*" id="fileInput" name="image" />
        </label>

        <img id="imagePreview" class="image-preview" alt="Prévisualisation de l'image" />

        <!-- Form fields for photo details -->
        <div id="photoDetails" style="display: none; margin-top: 20px; max-width: 600px;">
          <div style="margin-bottom: 15px;">
            <label for="titre" style="display: block; margin-bottom: 5px; font-weight: 600;">Titre de la photo *</label>
            <input type="text" id="titre" name="titre" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="Donnez un titre à votre photo">
          </div>

          <div style="margin-bottom: 15px;">
            <label for="description" style="display: block; margin-bottom: 5px; font-weight: 600;">Description</label>
            <textarea id="description" name="description" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; min-height: 80px; resize: vertical;" placeholder="Décrivez votre photo..."></textarea>
          </div>

          <div style="margin-bottom: 15px;">
            <label for="tags" style="display: block; margin-bottom: 5px; font-weight: 600;">Tags</label>
            <input type="text" id="tags" name="tags" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="portrait, nature, mariage... (séparés par des virgules)">
          </div>
        </div>

        <div id="uploadStatus" style="margin-top: 15px; text-align: center; display: none;"></div>

        <div class="actions">
          <button type="submit" id="postButton" disabled>📤 Poster</button>
          <button type="button" id="cancelButton">❌ Annuler</button>
          <button type="button" id="edit_button"><a href="editor.html" style="text-decoration: none; color: inherit;">Editer</a></button>
        </div>
      </form>
    </main>

    <!-- Right Sidebar -->

  </div>

 <script>
  const uploadZone = document.getElementById('uploadZone');
  const fileInput = document.getElementById('fileInput');
  const imagePreview = document.getElementById('imagePreview');
  const postButton = document.getElementById('postButton');
  const cancelButton = document.getElementById('cancelButton');
  const editButton = document.getElementById('editButton');

  // Quand on clique sur la zone, on ouvre le sélecteur de fichier
  uploadZone.addEventListener('click', () => fileInput.click());

  // Effets visuels lors du drag & drop
  uploadZone.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadZone.classList.add('dragover');
  });

  uploadZone.addEventListener('dragleave', () => {
    uploadZone.classList.remove('dragover');
  });

  uploadZone.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadZone.classList.remove('dragover');
    if (e.dataTransfer.files.length) {
      fileInput.files = e.dataTransfer.files;
      handleFile(fileInput.files[0]);
    }
  });

  // Lorsqu’un fichier est sélectionné
  fileInput.addEventListener('change', () => {
    if (fileInput.files.length) {
      handleFile(fileInput.files[0]);
    }
  });

  // Gérer le fichier image
  function handleFile(file) {
    if (!file.type.startsWith('image/')) {
      alert("Veuillez sélectionner un fichier image valide.");
      resetPreview();
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const imageDataURL = e.target.result;

      // Affiche l'aperçu
      imagePreview.src = imageDataURL;
      imagePreview.style.display = 'block';
      uploadZone.style.display = 'none';

      // Show photo details form
      document.getElementById('photoDetails').style.display = 'block';
      postButton.disabled = false;

      // Stocker l'image dans localStorage pour l’éditeur
      localStorage.setItem('uploadedImage', imageDataURL);
    };
    reader.readAsDataURL(file);
  }

  // Réinitialiser l’interface
  function resetPreview() {
    imagePreview.src = '';
    imagePreview.style.display = 'none';
    uploadZone.style.display = 'flex';
    document.getElementById('photoDetails').style.display = 'none';
    document.getElementById('uploadStatus').style.display = 'none';
    postButton.disabled = true;
    fileInput.value = '';
    document.getElementById('titre').value = '';
    document.getElementById('description').value = '';
    document.getElementById('tags').value = '';
    localStorage.removeItem('uploadedImage');
  }

  // Annuler
  cancelButton.addEventListener('click', resetPreview);

  // Handle form submission
  document.getElementById('uploadForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const statusDiv = document.getElementById('uploadStatus');
    const formData = new FormData(this);

    // Check if user is logged in
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    if (!user.id) {
      statusDiv.innerHTML = '<span style="color: red;">Vous devez être connecté pour poster une photo. <a href="login.html">Se connecter</a></span>';
      statusDiv.style.display = 'block';
      return;
    }

    // Check if user is a photographer
    if (user.user_type !== 'photographe') {
      statusDiv.innerHTML = '<span style="color: red;">Seuls les photographes peuvent poster des photos.</span>';
      statusDiv.style.display = 'block';
      return;
    }

    try {
      statusDiv.innerHTML = '<span style="color: blue;">Upload en cours...</span>';
      statusDiv.style.display = 'block';
      postButton.disabled = true;

      const response = await fetch('/api/photos/upload', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        statusDiv.innerHTML = '<span style="color: green;">✅ Photo postée avec succès!</span>';
        setTimeout(() => {
          resetPreview();
        }, 2000);
      } else {
        statusDiv.innerHTML = `<span style="color: red;">❌ Erreur: ${data.error}</span>`;
        postButton.disabled = false;
      }
    } catch (error) {
      console.error('Error:', error);
      statusDiv.innerHTML = '<span style="color: red;">❌ Erreur de connexion au serveur</span>';
      postButton.disabled = false;
    }
  });

 function openInlineEditor(imageSrc) {
  let existingEditor = document.getElementById('inlineEditor');
  if (existingEditor) return; // Empêche de créer plusieurs éditeurs

  const editorDiv = document.createElement('div');
  editorDiv.id = 'inlineEditor';
  editorDiv.style.marginTop = '40px';
  editorDiv.style.padding = '20px';
  editorDiv.style.border = '1.5px dashed #ccc';
  editorDiv.style.borderRadius = '10px';
  editorDiv.style.backgroundColor = '#fafafa';

  editorDiv.innerHTML = `
    <h3 style="text-align: center; margin-bottom: 20px;">🛠️ Éditeur d'image</h3>
    <img src="${imageSrc}" alt="Éditable" style="max-width:100%; border-radius: 10px; margin-bottom: 20px;" />
    <div style="text-align: center;">
      <button onclick="applyGrayscale()">🎨 Noir & Blanc</button>
      <button onclick="resetImage()">↩️ Réinitialiser</button>
    </div>
  `;

  document.querySelector('.content-wrapper').appendChild(editorDiv);
}

// Exemple d'effet de retouche
function applyGrayscale() {
  const editorImg = document.querySelector('#inlineEditor img');
  if (editorImg) {
    editorImg.style.filter = 'grayscale(100%)';
  }
}

function resetImage() {
  const editorImg = document.querySelector('#inlineEditor img');
  const original = localStorage.getItem('uploadedImage');
  if (editorImg && original) {
    editorImg.style.filter = '';
    editorImg.src = original;
  }
}
</script>


</body>
</html>
