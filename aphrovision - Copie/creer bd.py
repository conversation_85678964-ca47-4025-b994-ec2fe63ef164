import sqlite3
from datetime import datetime

# Connexion à la base de données
conn = sqlite3.connect("photographe_frelance.db")
cursor = conn.cursor()

# Création des tables
def create_tables():
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS client (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT,
            prenom TEXT,
            email TEXT,
            phone TEXT
        )
    ''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS photographe (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT,
            prenom TEXT,
            email TEXT,
            phone TEXT,
            wilaya TEXT,
            domaine TEXT,
            bio TEXT
        )
    ''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS projet (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            id_client INTEGER,
            id_photographe INTEGER,
            description TEXT,
            date_posted TEXT,
            image TEXT,
            FOREIGN KEY(id_client) REFERENCES client(id),
            FOREIGN KEY(id_photographe) REFERENCES photographe(id)
        )
    ''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS photo (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            id_photographe INTEGER,
            image TEXT,
            description TEXT,
            FOREIGN KEY(id_photographe) REFERENCES photographe(id)
        )
    ''')
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS annonce (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            categore TEXT,
            id_utilisateur INTEGER,
            etet TEXT,
            image TEXT,
            titre TEXT,
            description TEXT,
            prix REAL,
            date_posted TEXT
        )
    ''')
    conn.commit()

# Fonctions génériques
def ajouter(table, data):
    keys = ', '.join(data.keys())
    qmarks = ', '.join(['?'] * len(data))
    values = tuple(data.values())
    cursor.execute(f"INSERT INTO {table} ({keys}) VALUES ({qmarks})", values)
    conn.commit()
    print("Ajouté avec succès.")

def lister(table):
    cursor.execute(f"SELECT * FROM {table}")
    for row in cursor.fetchall():
        print(row)

def modifier(table, id_val, data):
    updates = ', '.join([f"{k}=?" for k in data.keys()])
    values = tuple(data.values()) + (id_val,)
    cursor.execute(f"UPDATE {table} SET {updates} WHERE id=?", values)
    conn.commit()
    print("Modifié avec succès.")

def supprimer(table, id_val):
    cursor.execute(f"DELETE FROM {table} WHERE id=?", (id_val,))
    conn.commit()
    print("Supprimé avec succès.")

def rechercher(table, colonne, valeur):
    cursor.execute(f"SELECT * FROM {table} WHERE {colonne} LIKE ?", ('%' + valeur + '%',))
    for row in cursor.fetchall():
        print(row)

# Interface simple
def menu():
    create_tables()
    while True:
        print("\n1. Ajouter\n2. Lister\n3. Modifier\n4. Supprimer\n5. Rechercher\n6. Quitter")
        choix = input("Choix : ")
        if choix == '1':
            table = input("Table à remplir : ")
            champs = input("Entrez les champs (clé=valeur, séparés par des virgules) : ")
            data = dict(item.split('=') for item in champs.split(','))
            ajouter(table, data)
        elif choix == '2':
            table = input("Table à afficher : ")
            lister(table)
        elif choix == '3':
            table = input("Table à modifier : ")
            id_val = input("ID à modifier : ")
            champs = input("Nouveaux champs (clé=valeur, séparés par des virgules) : ")
            data = dict(item.split('=') for item in champs.split(','))
            modifier(table, id_val, data)
        elif choix == '4':
            table = input("Table à supprimer : ")
            id_val = input("ID à supprimer : ")
            supprimer(table, id_val)
        elif choix == '5':
            table = input("Table à rechercher : ")
            colonne = input("Colonne à chercher : ")
            valeur = input("Valeur à chercher : ")
            rechercher(table, colonne, valeur)
        elif choix == '6':
            break
        else:
            print("Choix invalide.")

if __name__ == "__main__":
    menu()
    conn.close()
