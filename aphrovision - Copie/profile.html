<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Profil - Aphrovision</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', sans-serif;
    }

    body {
      background-color: #f7f9fb;
      color: #333;
    }

    /* Navigation */
    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: bold;
      color: black;
    }

    .nav-links a {
      margin: 0 12px;
      color: #333;
      text-decoration: none;
      font-weight: 500;
    }

    .nav-links a:hover {
      color: #ff4d4d;
    }

    /* Profile Section */
    .profile-section {
      display: flex;
      justify-content: space-between;
      padding: 50px;
      flex-wrap: wrap;
    }

    .profile-info {
      flex: 1;
      max-width: 600px;
    }

    .profile-info h1 {
      font-size: 2.2em;
      margin-bottom: 5px;
    }

    .handle {
      color: #777;
      font-size: 1em;
      margin-bottom: 15px;
    }

    .bio {
      font-size: 1.1em;
      margin-bottom: 30px;
    }

    .profile-info h3 {
      margin-top: 20px;
      margin-bottom: 10px;
      font-size: 1.2em;
    }

    .profile-info p {
      margin: 5px 0;
      font-size: 1em;
    }

    .add-button {
      background-color: #ffffff;
      color: rgb(8, 8, 8);
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      margin-bottom: 30px;
      font-size: 1em;
    }

    .add-button:hover {
      background-color:#629208; ;
    }

    /* Photo & Stats */
    .profile-photo-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .follow-stats {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin-bottom: 15px;
      font-size: 1em;
      color: #555;
    }

    .profile-photo {
      width: 350px;
      height: 350px;
      border-radius: 10px;
      overflow: hidden;
      border: 2px solid #ccc;
    }

    .profile-photo img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    @media (max-width: 768px) {
      .profile-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }

      .profile-info {
        max-width: 100%;
      }

      .profile-photo {
        width: 300px;
        height: 300px;
      }

      .follow-stats {
        gap: 20px;
      }
    }
  </style>
</head>
<body>

  <!-- Navigation -->
  <div class="navbar">
    <div class="logo">Aphrovision</div>
    <div class="nav-links">
      <a href="explore.html">EXPLORER</a>
      <a href="profile_view.html">PROFIL</a>
      <a href="photographers.html">PHOTOGRAPHES</a>
      <a href="poster.html">POSTER</a>
      <a href="#">MESSAGERIE</a>
      <a href="shop.html">MAGASIN</a>
    </div>

     <div class="notif-icon" aria-label="Notifications" role="button" tabindex="0">🔔</div>
  </div>

  <!-- Profile Section -->
  <div class="profile-section">

    <!-- Left Info -->
    <div class="profile-info">
      <button class="add-button">Ajouter</button>
      
      


      <h1>Hallali Tounsia</h1>
      <div class="handle">@tounsia</div>
      <div class="bio">Photographer from Algeria.</div>

      <h3>Informations générale</h3>
      <p><strong>Address :</strong> 16000 Alger</p>
      <p><strong>E-mail :</strong> <EMAIL></p>
      <p><strong>Phone :</strong> 0350157952</p>
      <p><strong>Appareil :</strong> Canon EOS 700d</p>
    </div>

    <!-- Photo + Stats -->
    <div class="profile-photo-wrapper">
      <div class="follow-stats">
        <div><strong>1</strong> Abonnées</div>
        <div><strong>3</strong> Abonnements</div>
      </div>
      <div class="profile-photo">
        <img src="photos3.jpg" alt="Hallali Tounsia" />
      </div>
    </div>

  </div>
  <script>
  const addButton = document.querySelector(".add-button");
  const infoSection = document.querySelector(".profile-info");

  addButton.addEventListener("click", () => {
    const newInfo = prompt("Entrez une nouvelle information (ex: Site web : www.exemple.com) :");
    if (newInfo && newInfo.trim() !== "") {
      // Créer un nouveau paragraphe et l'ajouter à la section des infos
      const p = document.createElement("p");
      p.textContent = newInfo;
      infoSection.appendChild(p);
    } else {
      alert("Aucune information ajoutée.");
    }
  });
</script>

</body>
</html>  