-- =====================================================
-- APHROVISION DATABASE SCHEMA FOR MYSQL/PHPMYADMIN
-- =====================================================
-- Copy and paste this entire script into phpMyAdmin SQL tab
-- =====================================================

-- Create database (uncomment if needed)
-- CREATE DATABASE aphrovision CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE aphrovision;

-- =====================================================
-- TABLE: users (Authentication)
-- =====================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('client', 'photographe') NOT NULL DEFAULT 'client',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active TINYINT(1) DEFAULT 1,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_user_type (user_type)
);

-- =====================================================
-- TABLE: client
-- =====================================================
CREATE TABLE client (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_email (email),
    INDEX idx_user_id (user_id)
);

-- =====================================================
-- TABLE: photographe
-- =====================================================
CREATE TABLE photographe (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNIQUE,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    wilaya VARCHAR(100) NOT NULL,
    domaine VARCHAR(100) NOT NULL,
    bio TEXT NOT NULL,
    profile_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_wilaya (wilaya),
    INDEX idx_domaine (domaine),
    INDEX idx_email (email),
    INDEX idx_user_id (user_id)
);

-- =====================================================
-- TABLE: projet
-- =====================================================
CREATE TABLE projet (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_client INT NOT NULL,
    id_photographe INT,
    titre VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    budget DECIMAL(10,2),
    status ENUM('ouvert', 'en_cours', 'termine', 'annule') DEFAULT 'ouvert',
    date_posted TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_limite DATE,
    image VARCHAR(255),
    FOREIGN KEY (id_client) REFERENCES client(id) ON DELETE CASCADE,
    FOREIGN KEY (id_photographe) REFERENCES photographe(id) ON DELETE SET NULL,
    INDEX idx_status (status),
    INDEX idx_date_posted (date_posted),
    INDEX idx_client (id_client),
    INDEX idx_photographe (id_photographe)
);

-- =====================================================
-- TABLE: photo
-- =====================================================
CREATE TABLE photo (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_photographe INT NOT NULL,
    titre VARCHAR(200) NOT NULL,
    image VARCHAR(255) NOT NULL,
    description TEXT,
    tags TEXT,
    likes INT DEFAULT 0,
    date_posted TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_public TINYINT(1) DEFAULT 1,
    FOREIGN KEY (id_photographe) REFERENCES photographe(id) ON DELETE CASCADE,
    INDEX idx_photographe (id_photographe),
    INDEX idx_date_posted (date_posted),
    INDEX idx_is_public (is_public),
    INDEX idx_likes (likes)
);

-- =====================================================
-- TABLE: annonce
-- =====================================================
CREATE TABLE annonce (
    id INT AUTO_INCREMENT PRIMARY KEY,
    categorie VARCHAR(100) NOT NULL,
    id_utilisateur INT NOT NULL,
    etat ENUM('disponible', 'vendu', 'reserve') DEFAULT 'disponible',
    image VARCHAR(255),
    titre VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    prix DECIMAL(10,2) NOT NULL,
    date_posted TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_utilisateur) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_categorie (categorie),
    INDEX idx_etat (etat),
    INDEX idx_prix (prix),
    INDEX idx_date_posted (date_posted),
    INDEX idx_utilisateur (id_utilisateur)
);

-- =====================================================
-- TABLE: messages
-- =====================================================
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    date_sent TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_read TINYINT(1) DEFAULT 0,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_date_sent (date_sent),
    INDEX idx_is_read (is_read)
);

-- =====================================================
-- SAMPLE DATA (Optional - for testing)
-- =====================================================

-- Insert sample users (password is 'password123' for all)
INSERT INTO users (username, email, password_hash, user_type) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJWZp/k/K', 'photographe'),
('client1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJWZp/k/K', 'client'),
('photographe1', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VJWZp/k/K', 'photographe');

-- Insert sample client
INSERT INTO client (user_id, nom, prenom, email, phone) VALUES
(2, 'Dupont', 'Jean', '<EMAIL>', '0123456789');

-- Insert sample photographers
INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio) VALUES
(1, 'Admin', 'Super', '<EMAIL>', '0987654321', 'Alger', 'Portrait', 'Photographe professionnel spécialisé en portraits'),
(3, 'Martin', 'Sophie', '<EMAIL>', '0555123456', 'Oran', 'Mariage', 'Photographe de mariage avec 5 ans d\'expérience');

-- =====================================================
-- NOTES FOR PHPMYADMIN USAGE:
-- =====================================================
-- 1. Copy this entire script
-- 2. Go to phpMyAdmin
-- 3. Select your database or create a new one
-- 4. Click on "SQL" tab
-- 5. Paste this script
-- 6. Click "Go" to execute
-- 
-- Default password for sample users is: "password123"
-- You can change passwords using the /api/register endpoint
-- =====================================================
