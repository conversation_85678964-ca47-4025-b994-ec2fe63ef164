<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Explorer - Aphrovision</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: white;
      color: #000;
    }

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 2em;
    }

    nav ul {
      list-style: none;
      display: flex;
      gap: 25px;
    }

    nav a {
      text-decoration: none;
      color: black;
      font-size: 1em;
      font-weight: 500;
      transition: color 0.3s;
    }

    nav a:hover {
      color: #888;
    }

    .notif-icon {
      font-size: 1.5em;
    }

    .search-section {
      text-align: center;
      margin: 30px 0;
    }

    .search-options span {
      margin: 0 10px;
      font-weight: bold;
      color: #333;
      cursor: pointer;
    }

    .search-options .active {
      color: #000;
      text-decoration: underline;
    }

    .search-box {
      margin-top: 15px;
    }

    .search-box input {
      width: 300px;
      padding: 10px;
      font-size: 1em;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .search-box button {
      padding: 10px 15px;
      margin-left: 5px;
      font-weight: bold;
      background-color: #333;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .gallery {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      padding: 20px 40px;
    }

    .gallery img {
      width: 100%;
      height: 200px;
      object-fit: cover;
      border-radius: 4px;
    }
  </style>
</head>
<body>

  <!-- En-tête -->
  <header>
    <div class="logo">Aphrovision</div>
    <nav>
      <ul>
        <li><a href="explore.html">Explorer</a></li>
        <li><a href="profile_view.html">Profil</a></li>
        <li><a href="photographers.html">Photographes</a></li>
        <li><a href="poster.html">Poster</a></li>
        <li><a href="#">Messagerie</a></li>
        <li><a href="shop.html">Magasin</a></li>
      </ul>
    </nav>
    <div class="notif-icon">🔔</div>
  </header>

  <!-- Barre de recherche -->
  <section class="search-section">
    <div class="search-options">
      <span class="active">Photos</span> | <span>Projects</span>
    </div>
    <div class="search-box">
      <input type="text" placeholder="Search" id="searchInput"/>
      <button id="searchBtn">Search</button>
    </div>
  </section>

  <!-- Galerie -->
  <section class="gallery" id="gallery">
    <img src="photos4.jpg" alt="enfants">
    <img src="photo2.PNG" alt="homme couché de solei">
    <img src="photo3.PNG" alt="homme photographe">
    <img src="photos5.jpg" alt="mariage">
    <img src="photos6.jpg" alt="fille">
    <img src="photos11.PNG" alt="soutenance">
    <img src="photos9.PNG" alt="nourriture">
    <img src="photos10.PNG" alt="tournage">
    <img src="photos7.PNG" alt="interview">
    <img src="photo20.png" alt="monument algerie">
    <img src="photo21.png" alt="mariage">
    <img src="photos2.jpg" alt="12">
  </section>

  <script>
  <!-- Script de recherche -->
  const searchBtn = document.getElementById("searchBtn");
const searchInput = document.getElementById("searchInput");
const images = document.querySelectorAll(".gallery img");

function filterImages() {
  const query = searchInput.value.toLowerCase().trim();

  images.forEach(img => {
    const altText = img.alt.toLowerCase();
    const match = altText.includes(query);

    if (query === "") {
      // Pas de recherche : tout visible et taille normale
      img.style.display = "block";
      img.classList.remove("large");
    } else {
      // Recherche en cours : affichage conditionnel + agrandissement des visibles
      img.style.display = match ? "block" : "none";
      if (match) {
        img.classList.add("large");
      } else {
        img.classList.remove("large");
      }
    }
  });
}

searchBtn.addEventListener("click", filterImages);
searchInput.addEventListener("keyup", filterImages);
</script>

</body>
</html>

