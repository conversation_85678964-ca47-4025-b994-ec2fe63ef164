"""
Database utility functions for SQLite operations
"""
import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

DATABASE = 'photographe_frelance.sqlite'

class DatabaseManager:
    """Database manager class for SQLite operations"""
    
    def __init__(self, db_path: str = DATABASE):
        self.db_path = db_path
    
    def get_connection(self) -> Optional[sqlite3.Connection]:
        """Get database connection with error handling"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            # Enable foreign key constraints
            conn.execute('PRAGMA foreign_keys = ON')
            return conn
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return None
    
    def execute_query(self, query: str, params: tuple = ()) -> Optional[List[sqlite3.Row]]:
        """Execute a SELECT query and return results"""
        conn = self.get_connection()
        if conn is None:
            return None
        
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)
            results = cursor.fetchall()
            conn.close()
            return results
        except sqlite3.Error as e:
            print(f"Query execution error: {e}")
            if conn:
                conn.close()
            return None
    
    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """Execute an INSERT, UPDATE, or DELETE query"""
        conn = self.get_connection()
        if conn is None:
            return False
        
        try:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            conn.close()
            return True
        except sqlite3.Error as e:
            print(f"Update execution error: {e}")
            if conn:
                conn.rollback()
                conn.close()
            return False
    
    def get_table_info(self, table_name: str) -> Optional[List[sqlite3.Row]]:
        """Get table schema information"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def table_exists(self, table_name: str) -> bool:
        """Check if a table exists"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_query(query, (table_name,))
        return result is not None and len(result) > 0
    
    def backup_database(self, backup_path: str) -> bool:
        """Create a backup of the database"""
        try:
            conn = self.get_connection()
            if conn is None:
                return False
            
            backup_conn = sqlite3.connect(backup_path)
            conn.backup(backup_conn)
            backup_conn.close()
            conn.close()
            return True
        except sqlite3.Error as e:
            print(f"Backup error: {e}")
            return False

# Utility functions for common database operations
def get_all_clients() -> Optional[List[Dict[str, Any]]]:
    """Get all clients from database"""
    db = DatabaseManager()
    results = db.execute_query("SELECT * FROM client")
    if results:
        return [dict(row) for row in results]
    return None

def get_all_photographers() -> Optional[List[Dict[str, Any]]]:
    """Get all photographers from database"""
    db = DatabaseManager()
    results = db.execute_query("SELECT * FROM photographe")
    if results:
        return [dict(row) for row in results]
    return None

def get_photographer_by_id(photographer_id: int) -> Optional[Dict[str, Any]]:
    """Get photographer by ID"""
    db = DatabaseManager()
    results = db.execute_query("SELECT * FROM photographe WHERE id = ?", (photographer_id,))
    if results and len(results) > 0:
        return dict(results[0])
    return None

def search_photographers(domaine: str = None, wilaya: str = None) -> Optional[List[Dict[str, Any]]]:
    """Search photographers by domain and/or wilaya"""
    db = DatabaseManager()
    query = "SELECT * FROM photographe WHERE 1=1"
    params = []
    
    if domaine:
        query += " AND domaine = ?"
        params.append(domaine)
    if wilaya:
        query += " AND wilaya = ?"
        params.append(wilaya)
    
    results = db.execute_query(query, tuple(params))
    if results:
        return [dict(row) for row in results]
    return None

def add_client(nom: str, prenom: str, email: str, phone: str) -> bool:
    """Add a new client"""
    db = DatabaseManager()
    query = "INSERT INTO client (nom, prenom, email, phone) VALUES (?, ?, ?, ?)"
    return db.execute_update(query, (nom, prenom, email, phone))

def add_photographer(nom: str, prenom: str, email: str, phone: str, 
                    wilaya: str, domaine: str, bio: str) -> bool:
    """Add a new photographer"""
    db = DatabaseManager()
    query = """INSERT INTO photographe (nom, prenom, email, phone, wilaya, domaine, bio) 
               VALUES (?, ?, ?, ?, ?, ?, ?)"""
    return db.execute_update(query, (nom, prenom, email, phone, wilaya, domaine, bio))

def get_database_stats() -> Dict[str, int]:
    """Get database statistics"""
    db = DatabaseManager()
    stats = {}
    
    tables = ['client', 'photographe', 'projet', 'photo', 'annonce']
    for table in tables:
        result = db.execute_query(f"SELECT COUNT(*) as count FROM {table}")
        if result:
            stats[table] = result[0]['count']
        else:
            stats[table] = 0
    
    return stats

# Initialize database manager instance
db_manager = DatabaseManager()
