<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Aphrovision Éditeur</title>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet">
  <style>
    /* Reset & fonts */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', sans-serif;
    }

    body {
      background-color: #e0e0e0; /* gris clair autour */
      height: 100vh;
      display: flex;
      justify-content: center;  /* centre horizontal */
      align-items: center;      /* centre vertical */
      padding: 20px;
      color: #333;
    }

    /* Conteneur principal - cadre blanc avec bordure plus foncée */
    .page-wrapper {
      background-color: white;
      border: 2px solid #333; /* plus foncé */
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      width: 95%;
      max-width: 1200px;
      padding: 25px 35px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    /* Header */
    header {
      width: 100%;
      border-bottom: 1px solid #444; /* plus foncé */
      padding-bottom: 15px;
      margin-bottom: 30px;
      text-align: center;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
    }

    header h1 {
      font-family: 'Playfair Display', serif;
      font-size: 2.5em;
      font-weight: bold;
      color: #222;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }

    .header-actions button {
      background: white;
      color: #333;
      border: 1.5px solid #444; /* plus foncé */
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 0.95em;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .header-actions button:hover {
      background-color: #629208;
      color: white;
      border-color: #629208;
    }

    /* Main editor container */
    main.editor-container {
      background: white;
      border-radius: 10px;
      border: 1.5px solid #333; /* bordure plus foncée */
      padding: 30px;
      width: 100%;
      display: flex;
      gap: 30px;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      box-sizing: border-box;
    }

    /* Controls columns */
    .controls-column {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .controls-column button {
      background: white;
      color: #333;
      border: 1.5px solid #333; /* bordure plus foncée */
      padding: 10px 16px;
      border-radius: 6px;
      font-size: 1em;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 150px;
    }

    .controls-column button:hover {
      background-color: #629208;
      color: white;
      border-color: #629208;
    }

    /* Image editor box */
    .image-editor {
      border: 1.5px dashed #333; /* bordure plus foncée */
      border-radius: 15px;
      padding: 20px 30px 40px;
      width: 350px;
      height: 350px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      background-color: rgba(50, 50, 50, 0.7);
      color: #eee;
      font-weight: 600;
      font-size: 1.1em;
      text-align: center;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;
      user-select: none;
      overflow: hidden;
    }

    /* Overlay sombre */
    .image-editor::before {
      content: "";
      position: absolute;
      top: 0; bottom: 0; left: 0; right: 0;
      background-color: rgba(0,0,0,0.35);
      border-radius: 15px;
      pointer-events: none;
      z-index: 0;
    }

    .image-editor > * {
      position: relative;
      z-index: 1;
    }

    /* Image affichée */
    .image-editor img {
      max-width: 100%;
      max-height: 100%;
      border-radius: 15px;
      position: absolute;
      top: 0;
      left: 0;
      object-fit: contain;
      z-index: 0;
      user-select: none;
      pointer-events: none;
    }

    /* Responsive */
    @media screen and (max-width: 768px) {
      main.editor-container {
        flex-direction: column;
        align-items: center;
      }

      .controls-column {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
      }

      .controls-column button {
        width: auto;
      }

      .image-editor {
        width: 90%;
        height: auto;
        padding: 30px 20px 40px;
      }

      .header-top {
        flex-direction: column;
        align-items: center;
        gap: 10px;
      }
    }
  </style>
</head>
<body>
  <div class="page-wrapper">
    <header>
      <div class="header-top">
        <h1>📷 Aphrovision</h1>
        <div class="header-actions">
          <button onclick="downloadPage()">⬇️ Télécharger</button>
          <button onclick="location.reload()">🔄 Rafraîchir</button>
        </div>
      </div>
    </header>

    <main class="editor-container">
      <div class="controls-column">
        <button data-filter="brightness">Brightness</button>
        <button data-filter="contrast">Contrast</button>
        <button data-filter="saturation">Saturation</button>
        <button data-filter="vibrance">Vibrance</button>
        <button data-filter="exposure">Exposure</button>
        <button data-filter="noise">Noise</button>
      </div>

      <div class="image-editor" id="imageEditor" tabindex="0" title="Déposez une image ou cliquez pour choisir">
        <span id="placeholder-text">Déposer l'image ici ou parcourir...</span>
        <!-- image insérée dynamiquement -->
        <img id="editableImage" alt="Image à éditer" style="display:none;">
      </div>

      <div class="controls-column">
        <button data-filter="sepia">Sepia</button>
        <button data-filter="hue">Hue</button>
        <button data-filter="blur">Blur</button>
        <button data-filter="gamma">Gamma</button>
        <button data-filter="clip">Clip</button>
        <button data-filter="sharpen">Sharpen</button>
      </div>
    </main>
  </div>

  <input type="file" id="fileInput" accept="image/*" style="display:none" />

  <script>
    const imageEditor = document.getElementById("imageEditor");
    const editableImage = document.getElementById("editableImage");
    const placeholderText = document.getElementById("placeholder-text");
    const fileInput = document.getElementById("fileInput");

    // Etat des filtres
    const filters = {
      brightness: 100,  // %
      contrast: 100,
      saturation: 100,
      vibrance: 100,    // non natif CSS, on ignore
      exposure: 100,    // non natif CSS, on ignore
      noise: 0,         // non natif CSS, ignore
      sepia: 0,         // 0 ou 100%
      hue: 0,           // deg
      blur: 0,          // px
      gamma: 1,         // non natif CSS
      clip: 0,          // ignore
      sharpen: 0        // ignore
    };

    // Charger image depuis localStorage au démarrage
    function loadImageFromStorage() {
      const dataUrl = localStorage.getItem("editedImage");
      if (dataUrl) {
        setImage(dataUrl);
      }
    }

    // Affiche l'image dans l'éditeur
    function setImage(src) {
      editableImage.src = src;
      editableImage.style.display = "block";
      placeholderText.style.display = "none";
      // Reset filtres
      resetFilters();
      applyFilters();
      // Stocker dans localStorage
      localStorage.setItem("editedImage", src);
    }

    // Supprime l'image et placeholder
    function clearImage() {
      editableImage.src = "";
      editableImage.style.display = "none";
      placeholderText.style.display = "block";
      localStorage.removeItem("editedImage");
    }

    // Gérer le drop
    imageEditor.addEventListener("dragover", e => {
      e.preventDefault();
      imageEditor.style.borderColor = "#629208";
      imageEditor.style.backgroundColor = "rgba(50, 50, 50, 0.85)";
    });

    imageEditor.addEventListener("dragleave", e => {
      e.preventDefault();
      imageEditor.style.borderColor = "#333";
      imageEditor.style.backgroundColor = "rgba(50, 50, 50, 0.7)";
    });

    imageEditor.addEventListener("drop", e => {
      e.preventDefault();
      imageEditor.style.borderColor = "#333";
      imageEditor.style.backgroundColor = "rgba(50, 50, 50, 0.7)";
      const files = e.dataTransfer.files;
      if (files.length === 0) return;

      const file = files[0];
      if (!file.type.startsWith("image/")) {
        alert("Veuillez déposer un fichier image.");
        return;
      }
      const reader = new FileReader();
      reader.onload = function(event) {
        setImage(event.target.result);
      };
      reader.readAsDataURL(file);
    });

    // Clic sur zone pour ouvrir input file
    imageEditor.addEventListener("click", () => {
      fileInput.click();
    });

    // Gestion input file
    fileInput.addEventListener("change", (e) => {
      const file = e.target.files[0];
      if (!file) return;

      if (!file.type.startsWith("image/")) {
        alert("Veuillez sélectionner un fichier image.");
        return;
      }

      const reader = new FileReader();
      reader.onload = function(event) {
        setImage(event.target.result);
      };
      reader.readAsDataURL(file);
      fileInput.value = ""; // reset input
    });

    // Reset filtres à l’état initial
    function resetFilters() {
      filters.brightness = 100;
      filters.contrast = 100;
      filters.saturation = 100;
      filters.sepia = 0;
      filters.hue = 0;
      filters.blur = 0;
      filters.noise=0;
      // Les autres non pris en compte
    }

    // Applique les filtres CSS à l'image
    function applyFilters() {
      const f = filters;
      // Compose la chaîne CSS filter
      let filterStr = 
        `brightness(${f.brightness}%) ` +
        `contrast(${f.contrast}%) ` +
        `saturate(${f.saturation}%) ` +
        `sepia(${f.sepia}%) ` +
        `hue-rotate(${f.hue}deg) ` +
        `blur(${f.blur}px)`;
      editableImage.style.filter = filterStr;
    }

    // Gestion clics sur boutons filtres
    const filterButtons = document.querySelectorAll("button[data-filter]");
    filterButtons.forEach(button => {
      button.addEventListener("click", () => {
        if (!editableImage.src) {
          alert("Veuillez déposer ou sélectionner une image d'abord.");
          return;
        }

        const filterName = button.getAttribute("data-filter");

        switch(filterName) {
          case "sepia":
            // toggle 0 <-> 100%
            filters.sepia = filters.sepia === 0 ? 100 : 0;
            break;

          case "brightness":
            filters.brightness = (filters.brightness === 100) ? 120 : 100;
            break;

          case "contrast":
            filters.contrast = (filters.contrast === 100) ? 150 : 100;
            break;

          case "saturation":
            filters.saturation = (filters.saturation === 100) ? 150 : 100;
            break;

          case "hue":
            filters.hue = (filters.hue + 90) % 360;
            break;

          case "blur":
            filters.blur = (filters.blur === 0) ? 2 : 0;
            break;

          // Les filtres ignorés :
          case "vibrance":
          case "exposure":
          case "noise":
          case "gamma":
          case "clip":
          case "sharpen":
            alert("Filtre non encore implémenté.");
            break;

          default:
            alert("Filtre inconnu.");
            break;
        }

        applyFilters();
      });
    });




    

    // Fonction download : télécharge la page HTML
    function downloadPage() {
      // On propose le contenu de la page HTML courante à télécharger
      const html = document.documentElement.outerHTML;
      const blob = new Blob([html], {type: "text/html"});
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "aphrovision-editor.html";
      a.click();
      URL.revokeObjectURL(url);
    }

    // Au chargement, on tente de restaurer l'image
    loadImageFromStorage();

  </script>
</body>
</html>
