<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Aphrovision-Shop</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: black;
      padding: 10px 30px;
      color: white;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 1000;
    }

    .navbar .logo {
      font-size: 24px;
      font-weight: bold;
      font-family: 'Playfair Display', serif;
    }

    .navbar .nav-links a {
      color: white;
      text-decoration: none;
      margin-left: 25px;
      font-weight: bold;
      font-size: 14px;
      letter-spacing: 1px;
    }

    .navbar .nav-links a:hover {
      color: #ff4d4d;
    }

    header {
      background-image:
        url('photos1.jpg'),
        url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1400&q=80');
      background-size: cover;
      background-position: center;
      color: white;
      text-align: center;
      padding: 140px 20px 60px;
      position: relative;
      margin-top: 60px;
    }

    header h1 {
      font-size: 36px;
      margin-bottom: 10px;
      font-family: 'Playfair Display', serif;
    }

    header p {
      font-size: 18px;
      margin-top: 0;
    }

    #search-bar {
      margin-top: 20px;
    }

    #search-bar input {
      padding: 10px;
      width: 300px;
      border: none;
      border-radius: 3px;
    }

    #search-bar button {
      padding: 10px 20px;
      background-color: #333;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }

    #search-bar button:hover {
      background-color: #555;
    }

    .annonce-button {
      position: absolute;
      top: 90px;
      right: 20px;
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 12px 18px;
      font-weight: bold;
      cursor: pointer;
      border-radius: 5px;
      user-select: none;
      transition: background-color 0.3s ease;
    }

    .annonce-button:hover {
      background-color: #cc3a3a;
    }

    .profile-info {
      margin: 20px auto;
      padding: 15px;
      border: 1px solid #ccc;
      background: white;
      max-width: 800px;
      min-height: 200px;
      border-radius: 6px;
    }

    .profile-info h3 {
      margin-top: 0;
      font-family: 'Playfair Display', serif;
      border-bottom: 2px solid #ff4d4d;
      padding-bottom: 10px;
      margin-bottom: 15px;
    }

    .announcement-item {
      background: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 10px;
      border-left: 4px solid #ff4d4d;
    }

    .announcement-item h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 1.1em;
    }

    .announcement-item p {
      margin: 5px 0;
      color: #666;
      font-size: 0.9em;
    }

    .announcement-price {
      font-weight: bold;
      color: #ff4d4d;
      font-size: 1.1em;
    }

    .announcement-meta {
      font-size: 0.8em;
      color: #999;
      margin-top: 8px;
    }

    .products-section {
      margin: 20px auto;
      padding: 15px;
      border: 1px solid #ccc;
      background: white;
      max-width: 800px;
      border-radius: 6px;
    }

    .products-section h3 {
      margin-top: 0;
      font-family: 'Playfair Display', serif;
      border-bottom: 2px solid #ff4d4d;
      padding-bottom: 10px;
      margin-bottom: 15px;
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
    }

    .product-card {
      background: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      text-align: center;
    }

    .product-card img {
      width: 100%;
      height: 150px;
      object-fit: cover;
      border-radius: 3px;
      margin-bottom: 10px;
    }

    .product-card h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 1em;
    }

    .product-card p {
      margin: 5px 0;
      color: #666;
      font-size: 0.85em;
    }

    .product-price {
      font-weight: bold;
      color: #ff4d4d;
      font-size: 1.1em;
      margin-top: 8px;
    }

    #annonceModal {
      display: none;
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: rgba(0,0,0,0.6);
      justify-content: center;
      align-items: center;
      z-index: 2000;
    }

    #annonceModal > div {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      position: relative;
    }

    #annonceModal textarea {
      width: 100%;
      font-size: 16px;
      padding: 10px;
      resize: vertical;
    }

    #annonceModal button {
      cursor: pointer;
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      font-weight: bold;
    }

    #annonceCancel {
      background: #ccc;
      margin-right: 10px;
      color: #333;
    }

    #annonceSubmit {
      background: #ff4d4d;
      color: white;
    }

    #annonceCancel:hover {
      background: #bbb;
    }

    #annonceSubmit:hover {
      background: #cc3a3a;
    }

    .loading {
      text-align: center;
      padding: 20px;
      color: #666;
    }

    .error {
      background: #ffebee;
      color: #c62828;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 15px;
      border: 1px solid #ffcdd2;
    }

    footer {
      text-align: center;
      padding: 20px;
      font-size: 14px;
      color: #777;
      margin-top: 40px;
    }
  </style>
</head>
<body>

<!-- Navbar -->
<div class="navbar">
  <div class="logo">Aphrovision</div>
  <div class="nav-links">
    <a href="explore.html">EXPLORER</a>
    <a href="profile_view.html">PROFIL</a>
    <a href="photographers.html">PHOTOGRAPHES</a>
    <a href="poster.html">POSTER</a>
    <a href="#">MESSAGERIE</a>
    <a href="shop.html">MAGASIN</a>
  </div>
  <div class="notif-icon" aria-label="Notifications" role="button" tabindex="0">🔔</div>
</div>

<!-- Header -->
<header>
  <button class="annonce-button">Déposer annonce</button>
  <h1>AphrovisionShop</h1>
  <p>Tous le monde peut vendre ou acheter les utilitaires de photographie</p>
  <div id="search-bar">
    <input type="text" id="searchInput" placeholder="Rechercher caméras, éclairage, objectifs..." />
    <button id="searchButton">Search</button>
  </div>
</header>

<!-- Products Section -->
<div class="products-section">
  <h3>🛍️ Produits Populaires</h3>
  <div class="products-grid" id="productsGrid">
    <!-- Products will be populated by JavaScript -->
  </div>
</div>

<!-- Annonces -->
<div class="profile-info">
  <h3>📢 Mes annonces</h3>
  <div id="errorMessage"></div>
  <div id="loadingMessage" class="loading">🔄 Chargement des annonces...</div>
  <div id="announcementsContainer">
    <!-- Announcements will be populated by JavaScript -->
  </div>
</div>

<!-- Modal Annonce -->
<div id="annonceModal">
  <div>
    <h2>Déposer une annonce</h2>
    <textarea id="annonceText" rows="6" placeholder="Écrivez votre annonce ici..."></textarea>
    <div style="margin-top:15px; text-align:right;">
      <button id="annonceCancel">Annuler</button>
      <button id="annonceSubmit">Envoyer</button>
    </div>
  </div>
</div>

<!-- Footer -->
<footer>
  &copy; 2025 AphrovisionShop. Tous droits réservés.
</footer>

<!-- Script -->
<script>
// Sample products data
const products = [
  {
    id: 1,
    name: "Canon EOS R5",
    description: "Appareil photo mirrorless professionnel 45MP avec vidéo 8K",
    price: 450000,
    category: "Caméras",
    image: "photo1.PNG"
  },
  {
    id: 2,
    name: "Sony A7 IV",
    description: "Appareil photo hybride 33MP avec stabilisation 5 axes",
    price: 380000,
    category: "Caméras",
    image: "photo2.PNG"
  },
  {
    id: 3,
    name: "Kit Éclairage LED",
    description: "Kit complet 3 panneaux LED avec trépieds et diffuseurs",
    price: 85000,
    category: "Éclairage",
    image: "photo3.PNG"
  },
  {
    id: 4,
    name: "Objectif Canon 24-70mm f/2.8",
    description: "Objectif zoom professionnel pour portraits et paysages",
    price: 220000,
    category: "Objectifs",
    image: "photo4.png"
  },
  {
    id: 5,
    name: "Trépied Manfrotto",
    description: "Trépied carbone professionnel avec rotule fluide",
    price: 65000,
    category: "Accessoires",
    image: "photo5.png"
  },
  {
    id: 6,
    name: "Flash Godox V1",
    description: "Flash cobra TTL avec tête ronde et batterie lithium",
    price: 45000,
    category: "Éclairage",
    image: "photo6.png"
  },
  {
    id: 7,
    name: "Drone DJI Mini 3",
    description: "Drone compact 4K avec gimbal 3 axes et 38min d'autonomie",
    price: 120000,
    category: "Drones",
    image: "photo7.png"
  },
  {
    id: 8,
    name: "Objectif Sony 85mm f/1.4",
    description: "Objectif portrait avec bokeh exceptionnel",
    price: 180000,
    category: "Objectifs",
    image: "photo8.png"
  }
];

let allAnnouncements = [];

function showError(message) {
  document.getElementById('errorMessage').innerHTML = `<div class="error">❌ ${message}</div>`;
}

function hideError() {
  document.getElementById('errorMessage').innerHTML = '';
}

function showLoading(show = true) {
  document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
}

function displayProducts(productsToShow = products) {
  const container = document.getElementById('productsGrid');

  container.innerHTML = productsToShow.map(product => `
    <div class="product-card">
      <img src="${product.image}" alt="${product.name}" onerror="this.style.display='none'">
      <h4>${product.name}</h4>
      <p>${product.description}</p>
      <div class="product-price">${product.price.toLocaleString()} DA</div>
    </div>
  `).join('');
}

function displayAnnouncements(announcements) {
  const container = document.getElementById('announcementsContainer');

  if (announcements.length === 0) {
    container.innerHTML = `
      <div style="text-align: center; padding: 20px; color: #666;">
        <p>Aucune annonce trouvée. Soyez le premier à publier une annonce!</p>
      </div>
    `;
    return;
  }

  container.innerHTML = announcements.map(announcement => `
    <div class="announcement-item">
      <h4>${announcement.titre}</h4>
      <p>${announcement.description}</p>
      <p><strong>Catégorie:</strong> ${announcement.categore}</p>
      <p><strong>Statut:</strong> ${announcement.etet}</p>
      <div class="announcement-price">${announcement.prix.toLocaleString()} DA</div>
      <div class="announcement-meta">
        Par ${announcement.user_name} (${announcement.user_type}) -
        ${new Date(announcement.date_posted).toLocaleDateString('fr-FR')}
      </div>
    </div>
  `).join('');
}

async function loadAnnouncements() {
  try {
    hideError();
    showLoading(true);

    const response = await fetch('/api/announcements');

    if (response.ok) {
      const announcements = await response.json();
      console.log('Announcements loaded:', announcements);

      allAnnouncements = announcements;
      displayAnnouncements(announcements);
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error loading announcements:', error);
    showError(`Erreur lors du chargement des annonces: ${error.message}`);
  } finally {
    showLoading(false);
  }
}

function searchProducts(query) {
  const searchTerm = query.toLowerCase();
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm) ||
    product.description.toLowerCase().includes(searchTerm) ||
    product.category.toLowerCase().includes(searchTerm)
  );

  displayProducts(filteredProducts);

  // Scroll to products section
  document.querySelector('.products-section').scrollIntoView({
    behavior: 'smooth'
  });
}

document.addEventListener("DOMContentLoaded", () => {
  const annonceBtn = document.querySelector(".annonce-button");
  const annonceModal = document.getElementById("annonceModal");
  const annonceCancel = document.getElementById("annonceCancel");
  const annonceSubmit = document.getElementById("annonceSubmit");
  const annonceText = document.getElementById("annonceText");

  // Load initial data
  displayProducts();
  loadAnnouncements();

  // Modal functionality
  annonceBtn.addEventListener("click", () => {
    annonceText.value = "";
    annonceModal.style.display = "flex";
  });

  annonceCancel.addEventListener("click", () => {
    annonceModal.style.display = "none";
  });

  annonceSubmit.addEventListener("click", () => {
    const texte = annonceText.value.trim();
    if (texte) {
      // Add the announcement to the display
      const newAnnouncement = {
        titre: "Nouvelle annonce",
        description: texte,
        categore: "Service",
        etet: "Disponible",
        prix: 0,
        user_name: "Utilisateur",
        user_type: "Client",
        date_posted: new Date().toISOString()
      };

      // Add to beginning of array and redisplay
      allAnnouncements.unshift(newAnnouncement);
      displayAnnouncements(allAnnouncements);

      annonceModal.style.display = "none";
      alert("Annonce ajoutée avec succès!");
    } else {
      alert("Veuillez écrire une annonce avant d'envoyer.");
    }
  });

  annonceModal.addEventListener("click", (e) => {
    if (e.target === annonceModal) {
      annonceModal.style.display = "none";
    }
  });

  // Search functionality
  document.getElementById("searchButton").addEventListener("click", () => {
    const searchValue = document.getElementById("searchInput").value.trim();
    if (searchValue !== "") {
      searchProducts(searchValue);
    } else {
      displayProducts(); // Show all products
    }
  });

  // Search on Enter key
  document.getElementById("searchInput").addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      document.getElementById("searchButton").click();
    }
  });
});
</script>

</body>
</html>
