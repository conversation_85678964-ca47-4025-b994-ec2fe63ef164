<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Aphrovision-Shop</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f8f9fa;
      color: #333;
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 15px 40px;
      color: white;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 1000;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .navbar .logo {
      font-size: 28px;
      font-weight: bold;
      font-family: 'Playfair Display', serif;
    }

    .navbar .nav-links a {
      color: white;
      text-decoration: none;
      margin-left: 25px;
      font-weight: 600;
      font-size: 14px;
      letter-spacing: 1px;
      transition: color 0.3s;
    }

    .navbar .nav-links a:hover {
      color: #ffd700;
    }

    .notif-icon {
      font-size: 20px;
      cursor: pointer;
      padding: 8px;
      border-radius: 50%;
      transition: background-color 0.3s;
    }

    .notif-icon:hover {
      background-color: rgba(255,255,255,0.2);
    }

    header {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)),
                  url('photos1.jpg');
      background-size: cover;
      background-position: center;
      color: white;
      text-align: center;
      padding: 120px 20px 80px;
      margin-top: 70px;
      position: relative;
    }

    header h1 {
      font-size: 3.5em;
      margin-bottom: 15px;
      font-family: 'Playfair Display', serif;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    header p {
      font-size: 1.3em;
      margin-bottom: 30px;
      opacity: 0.95;
    }

    .search-section {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-top: 30px;
      flex-wrap: wrap;
    }

    .search-container {
      display: flex;
      background: white;
      border-radius: 50px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      max-width: 500px;
      width: 100%;
    }

    #searchInput {
      flex: 1;
      padding: 15px 25px;
      border: none;
      font-size: 16px;
      outline: none;
    }

    #searchButton {
      padding: 15px 30px;
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      color: white;
      border: none;
      cursor: pointer;
      font-weight: 600;
      transition: background 0.3s;
    }

    #searchButton:hover {
      background: linear-gradient(135deg, #ee5a24 0%, #d63031 100%);
    }

    .annonce-button {
      position: absolute;
      top: 20px;
      right: 30px;
      background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
      color: white;
      border: none;
      padding: 12px 25px;
      font-weight: 600;
      cursor: pointer;
      border-radius: 25px;
      font-size: 14px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0,184,148,0.3);
    }

    .annonce-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0,184,148,0.4);
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }

    .section-header {
      text-align: center;
      margin-bottom: 40px;
    }

    .section-title {
      font-size: 2.5em;
      color: #333;
      margin-bottom: 10px;
      font-family: 'Playfair Display', serif;
    }

    .section-subtitle {
      font-size: 1.1em;
      color: #666;
    }

    .products-section {
      margin-bottom: 60px;
    }

    .products-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 25px;
      margin-bottom: 40px;
    }

    .product-card {
      background: white;
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .product-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
    }

    .product-info {
      padding: 20px;
    }

    .product-name {
      font-size: 1.3em;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .product-description {
      color: #666;
      font-size: 0.9em;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .product-price {
      font-size: 1.4em;
      font-weight: bold;
      color: #00b894;
      margin-bottom: 15px;
    }

    .product-category {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 0.8em;
      display: inline-block;
    }

    .announcements-section {
      background: white;
      border-radius: 20px;
      padding: 40px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .announcements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 25px;
    }

    .announcement-card {
      background: #f8f9fa;
      border-radius: 15px;
      padding: 25px;
      border-left: 5px solid #667eea;
      transition: transform 0.3s ease;
    }

    .announcement-card:hover {
      transform: translateX(5px);
    }

    .announcement-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
    }

    .announcement-title {
      font-size: 1.3em;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .announcement-category {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 0.8em;
    }

    .announcement-status {
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 0.8em;
      font-weight: 600;
    }

    .status-disponible { background: #00b894; color: white; }
    .status-reserve { background: #fdcb6e; color: #333; }
    .status-vendu { background: #e17055; color: white; }

    .announcement-description {
      color: #666;
      margin-bottom: 15px;
      line-height: 1.5;
    }

    .announcement-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }

    .announcement-price {
      font-size: 1.4em;
      font-weight: bold;
      color: #00b894;
    }

    .announcement-user {
      font-size: 0.9em;
      color: #667eea;
      font-weight: 600;
    }

    .loading {
      text-align: center;
      padding: 40px;
      font-size: 1.2em;
      color: #666;
    }

    .error {
      background: #ff7675;
      color: white;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 20px;
      text-align: center;
    }

    #annonceModal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      justify-content: center;
      align-items: center;
      z-index: 2000;
    }

    .modal-content {
      background: white;
      padding: 30px;
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    }

    .modal-content h2 {
      margin-bottom: 20px;
      color: #333;
      font-family: 'Playfair Display', serif;
    }

    .modal-content textarea {
      width: 100%;
      padding: 15px;
      border: 2px solid #eee;
      border-radius: 10px;
      font-size: 16px;
      resize: vertical;
      font-family: inherit;
    }

    .modal-content textarea:focus {
      border-color: #667eea;
      outline: none;
    }

    .modal-buttons {
      margin-top: 20px;
      text-align: right;
    }

    .modal-buttons button {
      padding: 12px 25px;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      margin-left: 10px;
      transition: background 0.3s;
    }

    #annonceCancel {
      background: #ddd;
      color: #333;
    }

    #annonceCancel:hover {
      background: #ccc;
    }

    #annonceSubmit {
      background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
      color: white;
    }

    #annonceSubmit:hover {
      background: linear-gradient(135deg, #00a085 0%, #008f7a 100%);
    }

    footer {
      text-align: center;
      padding: 40px 20px;
      background: #333;
      color: white;
      margin-top: 60px;
    }

    @media (max-width: 768px) {
      .navbar {
        padding: 10px 20px;
      }

      .navbar .nav-links a {
        margin-left: 15px;
        font-size: 12px;
      }

      header h1 {
        font-size: 2.5em;
      }

      .search-container {
        margin: 0 20px;
      }

      .products-grid,
      .announcements-grid {
        grid-template-columns: 1fr;
      }

      .annonce-button {
        position: static;
        margin: 20px auto;
        display: block;
        width: fit-content;
      }
    }
  </style>
</head>
<body>

<!-- Navbar -->
<div class="navbar">
  <div class="logo">Aphrovision</div>
  <div class="nav-links">
    <a href="explore.html">EXPLORER</a>
    <a href="profile_view.html">PROFIL</a>
    <a href="photographers.html">PHOTOGRAPHES</a>
    <a href="poster.html">POSTER</a>
    <a href="#">MESSAGERIE</a>
    <a href="shop.html">MAGASIN</a>
    <a href="mes_annonces.html">MES ANNONCES</a>
  </div>
  <div class="notif-icon" aria-label="Notifications" role="button" tabindex="0">🔔</div>
</div>

<!-- Header -->
<header>
  <button class="annonce-button">📝 Déposer annonce</button>
  <h1>AphrovisionShop</h1>
  <p>Marketplace pour équipements et services photographiques</p>
  <div class="search-section">
    <div class="search-container">
      <input type="text" id="searchInput" placeholder="Rechercher caméras, éclairage, objectifs..." />
      <button id="searchButton">🔍 Rechercher</button>
    </div>
  </div>
</header>

<!-- Main Content -->
<div class="container">
  <!-- Products Section -->
  <section class="products-section">
    <div class="section-header">
      <h2 class="section-title">🛍️ Produits Populaires</h2>
      <p class="section-subtitle">Découvrez notre sélection d'équipements photographiques</p>
    </div>
    <div class="products-grid" id="productsGrid">
      <!-- Products will be populated by JavaScript -->
    </div>
  </section>

  <!-- Announcements Section -->
  <section class="announcements-section">
    <div class="section-header">
      <h2 class="section-title">📢 Annonces Récentes</h2>
      <p class="section-subtitle">Dernières offres de notre communauté</p>
    </div>
    <div id="errorMessage"></div>
    <div id="loadingMessage" class="loading">🔄 Chargement des annonces...</div>
    <div class="announcements-grid" id="announcementsGrid">
      <!-- Announcements will be populated by JavaScript -->
    </div>
  </section>
</div>

<!-- Modal Annonce -->
<div id="annonceModal">
  <div class="modal-content">
    <h2>📝 Déposer une annonce</h2>
    <textarea id="annonceText" rows="6" placeholder="Décrivez votre produit ou service..."></textarea>
    <div class="modal-buttons">
      <button id="annonceCancel">Annuler</button>
      <button id="annonceSubmit">Publier</button>
    </div>
  </div>
</div>

<!-- Footer -->
<footer>
  &copy; 2025 AphrovisionShop. Tous droits réservés.
</footer>

<!-- Script -->
<script>
// Sample products data
const products = [
  {
    id: 1,
    name: "Canon EOS R5",
    description: "Appareil photo mirrorless professionnel 45MP avec vidéo 8K",
    price: 450000,
    category: "Caméras",
    image: "photo1.PNG"
  },
  {
    id: 2,
    name: "Sony A7 IV",
    description: "Appareil photo hybride 33MP avec stabilisation 5 axes",
    price: 380000,
    category: "Caméras",
    image: "photo2.PNG"
  },
  {
    id: 3,
    name: "Kit Éclairage LED",
    description: "Kit complet 3 panneaux LED avec trépieds et diffuseurs",
    price: 85000,
    category: "Éclairage",
    image: "photo3.PNG"
  },
  {
    id: 4,
    name: "Objectif Canon 24-70mm f/2.8",
    description: "Objectif zoom professionnel pour portraits et paysages",
    price: 220000,
    category: "Objectifs",
    image: "photo4.png"
  },
  {
    id: 5,
    name: "Trépied Manfrotto",
    description: "Trépied carbone professionnel avec rotule fluide",
    price: 65000,
    category: "Accessoires",
    image: "photo5.png"
  },
  {
    id: 6,
    name: "Flash Godox V1",
    description: "Flash cobra TTL avec tête ronde et batterie lithium",
    price: 45000,
    category: "Éclairage",
    image: "photo6.png"
  },
  {
    id: 7,
    name: "Drone DJI Mini 3",
    description: "Drone compact 4K avec gimbal 3 axes et 38min d'autonomie",
    price: 120000,
    category: "Drones",
    image: "photo7.png"
  },
  {
    id: 8,
    name: "Objectif Sony 85mm f/1.4",
    description: "Objectif portrait avec bokeh exceptionnel",
    price: 180000,
    category: "Objectifs",
    image: "photo8.png"
  }
];

let allAnnouncements = [];

function showError(message) {
  document.getElementById('errorMessage').innerHTML = `<div class="error">❌ ${message}</div>`;
}

function hideError() {
  document.getElementById('errorMessage').innerHTML = '';
}

function showLoading(show = true) {
  document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
}

function displayProducts(productsToShow = products) {
  const container = document.getElementById('productsGrid');

  container.innerHTML = productsToShow.map(product => `
    <div class="product-card">
      <img src="${product.image}" alt="${product.name}" class="product-image" onerror="this.style.display='none'">
      <div class="product-info">
        <h3 class="product-name">${product.name}</h3>
        <p class="product-description">${product.description}</p>
        <div class="product-price">${product.price.toLocaleString()} DA</div>
        <span class="product-category">${product.category}</span>
      </div>
    </div>
  `).join('');
}

function displayAnnouncements(announcements) {
  const container = document.getElementById('announcementsGrid');

  if (announcements.length === 0) {
    container.innerHTML = `
      <div style="text-align: center; padding: 40px; grid-column: 1 / -1;">
        <h3>Aucune annonce trouvée</h3>
        <p>Soyez le premier à publier une annonce!</p>
      </div>
    `;
    return;
  }

  container.innerHTML = announcements.map(announcement => {
    const statusClass = announcement.etet.toLowerCase().replace('é', 'e').replace(' ', '-');

    return `
      <div class="announcement-card">
        <div class="announcement-header">
          <div>
            <h3 class="announcement-title">${announcement.titre}</h3>
            <span class="announcement-category">${announcement.categore}</span>
          </div>
          <span class="announcement-status status-${statusClass}">${announcement.etet}</span>
        </div>

        <p class="announcement-description">${announcement.description}</p>

        <div class="announcement-footer">
          <div class="announcement-price">${announcement.prix.toLocaleString()} DA</div>
          <div class="announcement-user">👤 ${announcement.user_name}</div>
        </div>
      </div>
    `;
  }).join('');
}

async function loadAnnouncements() {
  try {
    hideError();
    showLoading(true);

    const response = await fetch('/api/announcements');

    if (response.ok) {
      const announcements = await response.json();
      console.log('Announcements loaded:', announcements);

      allAnnouncements = announcements;
      displayAnnouncements(announcements.slice(0, 6)); // Show only first 6
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Error loading announcements:', error);
    showError(`Erreur lors du chargement des annonces: ${error.message}`);
  } finally {
    showLoading(false);
  }
}

function searchProducts(query) {
  const searchTerm = query.toLowerCase();
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm) ||
    product.description.toLowerCase().includes(searchTerm) ||
    product.category.toLowerCase().includes(searchTerm)
  );

  displayProducts(filteredProducts);

  // Scroll to products section
  document.querySelector('.products-section').scrollIntoView({
    behavior: 'smooth'
  });
}

document.addEventListener("DOMContentLoaded", () => {
  const annonceBtn = document.querySelector(".annonce-button");
  const annonceModal = document.getElementById("annonceModal");
  const annonceCancel = document.getElementById("annonceCancel");
  const annonceSubmit = document.getElementById("annonceSubmit");
  const annonceText = document.getElementById("annonceText");

  // Load initial data
  displayProducts();
  loadAnnouncements();

  // Modal functionality
  annonceBtn.addEventListener("click", () => {
    annonceText.value = "";
    annonceModal.style.display = "flex";
  });

  annonceCancel.addEventListener("click", () => {
    annonceModal.style.display = "none";
  });

  annonceSubmit.addEventListener("click", () => {
    const texte = annonceText.value.trim();
    if (texte) {
      // Here you would normally send to server
      alert("Annonce publiée avec succès! (Fonctionnalité de sauvegarde à implémenter)");
      annonceModal.style.display = "none";
    } else {
      alert("Veuillez écrire une annonce avant de publier.");
    }
  });

  annonceModal.addEventListener("click", (e) => {
    if (e.target === annonceModal) {
      annonceModal.style.display = "none";
    }
  });

  // Search functionality
  document.getElementById("searchButton").addEventListener("click", () => {
    const searchValue = document.getElementById("searchInput").value.trim();
    if (searchValue !== "") {
      searchProducts(searchValue);
    } else {
      displayProducts(); // Show all products
    }
  });

  // Search on Enter key
  document.getElementById("searchInput").addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      document.getElementById("searchButton").click();
    }
  });
});
</script>

</body>
</html>
