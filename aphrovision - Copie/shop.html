<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Aphrovision-Shop</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background-color: #f4f4f4;
    }

    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: black;
      padding: 10px 30px;
      color: white;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 1000;
    }

    .navbar .logo {
      font-size: 24px;
      font-weight: bold;
      font-family: 'Playfair Display', serif;
    }

    .navbar .nav-links a {
      color: white;
      text-decoration: none;
      margin-left: 25px;
      font-weight: bold;
      font-size: 14px;
      letter-spacing: 1px;
    }

    .navbar .nav-links a:hover {
      color: #ff4d4d;
    }

    header {
      background-image:
        url('photos1.jpg'),
        url('https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=1400&q=80');
      background-size: cover;
      background-position: center;
      color: white;
      text-align: center;
      padding: 140px 20px 60px;
      position: relative;
      margin-top: 60px;
    }

    header h1 {
      font-size: 36px;
      margin-bottom: 10px;
      font-family: 'Playfair Display', serif;
    }

    header p {
      font-size: 18px;
      margin-top: 0;
    }

    #search-bar {
      margin-top: 20px;
    }

    #search-bar input {
      padding: 10px;
      width: 300px;
      border: none;
      border-radius: 3px;
    }

    #search-bar button {
      padding: 10px 20px;
      background-color: #333;
      color: white;
      border: none;
      border-radius: 3px;
      cursor: pointer;
    }

    #search-bar button:hover {
      background-color: #555;
    }

    .annonce-button {
      position: absolute;
      top: 90px;
      right: 20px;
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 12px 18px;
      font-weight: bold;
      cursor: pointer;
      border-radius: 5px;
      user-select: none;
      transition: background-color 0.3s ease;
    }

    .annonce-button:hover {
      background-color: #cc3a3a;
    }

    .profile-info {
      margin: 20px auto;
      padding: 15px;
      border: 1px solid #ccc;
      background: white;
      max-width: 600px;
      min-height: 100px;
      border-radius: 6px;
    }

    .profile-info h3 {
      margin-top: 0;
      font-family: 'Playfair Display', serif;
    }

    #annonceModal {
      display: none;
      position: fixed;
      top: 0; left: 0;
      width: 100%; height: 100%;
      background: rgba(0,0,0,0.6);
      justify-content: center;
      align-items: center;
      z-index: 2000;
    }

    #annonceModal > div {
      background: white;
      padding: 20px;
      border-radius: 8px;
      width: 90%;
      max-width: 400px;
      position: relative;
    }

    #annonceModal textarea {
      width: 100%;
      font-size: 16px;
      padding: 10px;
      resize: vertical;
    }

    #annonceModal button {
      cursor: pointer;
      padding: 8px 15px;
      border: none;
      border-radius: 4px;
      font-weight: bold;
    }

    #annonceCancel {
      background: #ccc;
      margin-right: 10px;
      color: #333;
    }

    #annonceSubmit {
      background: #ff4d4d;
      color: white;
    }

    #annonceCancel:hover {
      background: #bbb;
    }

    #annonceSubmit:hover {
      background: #cc3a3a;
    }

    footer {
      text-align: center;
      padding: 20px;
      font-size: 14px;
      color: #777;
      margin-top: 40px;
    }
  </style>
</head>
<body>

<!-- Navbar -->
<div class="navbar">
  <div class="logo">Aphrovision</div>
  <div class="nav-links">
    <a href="explore.html">EXPLORER</a>
    <a href="profile_view.html">PROFIL</a>
    <a href="photographers.html">PHOTOGRAPHES</a>
    <a href="poster.html">POSTER</a>
    <a href="#">MESSAGERIE</a>
    <a href="shop.html">MAGASIN</a>
  </div>
  <div class="notif-icon" aria-label="Notifications" role="button" tabindex="0">🔔</div>
</div>

<!-- Header -->
<header>
  <button class="annonce-button">Déposer annonce</button>
  <h1>AphrovisionShop</h1>
  <p>Tous le monde peut vendre ou acheter les utilitaires de photographie</p>
  <div id="search-bar">
    <input type="text" id="searchInput" placeholder="Recherche" />
    <button id="searchButton">Search</button>
  </div>
</header>

<!-- Annonces -->
<div class="profile-info">
  <h3>Mes annonces</h3>
</div>

<!-- Modal Annonce -->
<div id="annonceModal">
  <div>
    <h2>Déposer une annonce</h2>
    <textarea id="annonceText" rows="6" placeholder="Écrivez votre annonce ici..."></textarea>
    <div style="margin-top:15px; text-align:right;">
      <button id="annonceCancel">Annuler</button>
      <button id="annonceSubmit">Envoyer</button>
    </div>
  </div>
</div>

<!-- Footer -->
<footer>
  &copy; 2025 AphrovisionShop. Tous droits réservés.
</footer>

<!-- Script -->
<script>
document.addEventListener("DOMContentLoaded", () => {
  const annonceBtn = document.querySelector(".annonce-button");
  const infoSection = document.querySelector(".profile-info");
  const annonceModal = document.getElementById("annonceModal");
  const annonceCancel = document.getElementById("annonceCancel");
  const annonceSubmit = document.getElementById("annonceSubmit");
  const annonceText = document.getElementById("annonceText");

  annonceBtn.addEventListener("click", () => {
    annonceText.value = "";
    annonceModal.style.display = "flex";
  });

  annonceCancel.addEventListener("click", () => {
    annonceModal.style.display = "none";
  });

  annonceSubmit.addEventListener("click", () => {
    const texte = annonceText.value.trim();
    if (texte) {
      const p = document.createElement("p");
      p.textContent = texte;
      infoSection.appendChild(p);
      annonceModal.style.display = "none";
    } else {
      alert("Veuillez écrire une annonce avant d'envoyer.");
    }
  });

  annonceModal.addEventListener("click", (e) => {
    if (e.target === annonceModal) {
      annonceModal.style.display = "none";
    }
  });

  // 🔍 Fonction de recherche
  document.getElementById("searchButton").addEventListener("click", () => {
    const searchValue = document.getElementById("searchInput").value.trim();
    if (searchValue !== "") {
      window.location.href = `product.html?query=${encodeURIComponent(searchValue)}`;
    } else {
      alert("Veuillez entrer un mot-clé.");
    }
  });
});
</script>

</body>
</html>
