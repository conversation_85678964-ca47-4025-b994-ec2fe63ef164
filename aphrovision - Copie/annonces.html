<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Annonces - Aphrovision</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f2f2f2;
      color: #333;
    }

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 2em;
      color: #000000;
    }

    nav ul {
      list-style: none;
      display: flex;
      gap: 25px;
    }

    nav a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
    }

    nav a:hover {
      color: #f73535;
    }

    .container {
      max-width: 1200px;
      margin: 40px auto;
      padding: 0 20px;
    }

    .page-header {
      background-color: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
      text-align: center;
    }

    .page-title {
      font-size: 2.5em;
      margin-bottom: 10px;
      color: #333;
    }

    .page-subtitle {
      font-size: 1.1em;
      color: #666;
    }

    .filters-section {
      background-color: white;
      padding: 25px;
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
      margin-bottom: 30px;
    }

    .filters {
      display: flex;
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .filter-group label {
      font-weight: 600;
      color: #333;
    }

    select, button {
      padding: 10px 15px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 1em;
      background-color: white;
    }

    button {
      background-color: #007bff;
      color: white;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: #0056b3;
    }

    .stats {
      display: flex;
      gap: 20px;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .stat-card {
      background: white;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      flex: 1;
      min-width: 150px;
    }

    .stat-number {
      font-size: 2em;
      font-weight: bold;
      color: #007bff;
    }

    .stat-label {
      color: #666;
      margin-top: 5px;
    }

    .announcements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 25px;
    }

    .announcement-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .announcement-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .announcement-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
    }

    .announcement-content {
      padding: 20px;
    }

    .announcement-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
    }

    .announcement-title {
      font-size: 1.3em;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }

    .announcement-category {
      background: #007bff;
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 0.85em;
      font-weight: 500;
    }

    .announcement-status {
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 0.85em;
      font-weight: 500;
    }

    .status-disponible {
      background: #28a745;
      color: white;
    }

    .status-reserve {
      background: #ffc107;
      color: #333;
    }

    .status-vendu {
      background: #dc3545;
      color: white;
    }

    .announcement-description {
      color: #666;
      line-height: 1.5;
      margin-bottom: 15px;
    }

    .announcement-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 15px;
      border-top: 1px solid #eee;
    }

    .announcement-price {
      font-size: 1.4em;
      font-weight: bold;
      color: #28a745;
    }

    .announcement-user {
      font-size: 0.9em;
      color: #666;
    }

    .announcement-date {
      font-size: 0.85em;
      color: #999;
    }

    .loading {
      text-align: center;
      padding: 50px;
      font-size: 1.2em;
      color: #666;
    }

    .no-results {
      text-align: center;
      padding: 50px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 20px;
      border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
      .filters {
        flex-direction: column;
        align-items: stretch;
      }
      
      .stats {
        flex-direction: column;
      }
      
      .announcements-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>

  <header>
    <div class="logo">Aphrovision</div>
    <nav>
      <ul>
        <li><a href="explore.html">Explorer</a></li>
        <li><a href="profile_view.html">Profil</a></li>
        <li><a href="photographers.html">Photographes</a></li>
        <li><a href="poster.html">Poster</a></li>
        <li><a href="#">Messagerie</a></li>
        <li><a href="shop.html">Magasin</a></li>
        <li><a href="annonces.html">Annonces</a></li>
      </ul>
    </nav>
    <div class="notif-icon">🔔</div>
  </header>

  <div class="container">
    <div class="page-header">
      <h1 class="page-title">📢 Annonces</h1>
      <p class="page-subtitle">Découvrez les services, formations et équipements proposés par notre communauté</p>
    </div>

    <div class="filters-section">
      <div class="filters">
        <div class="filter-group">
          <label for="categoryFilter">Catégorie :</label>
          <select id="categoryFilter">
            <option value="">Toutes les catégories</option>
            <option value="Service">Service</option>
            <option value="Matériel">Matériel</option>
            <option value="Formation">Formation</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="statusFilter">Statut :</label>
          <select id="statusFilter">
            <option value="">Tous les statuts</option>
            <option value="Disponible">Disponible</option>
            <option value="Réservé">Réservé</option>
            <option value="Vendu">Vendu</option>
          </select>
        </div>

        <button onclick="applyFilters()">🔍 Filtrer</button>
        <button onclick="loadAllAnnouncements()" style="background-color: #28a745;">🔄 Tout Afficher</button>
      </div>
    </div>

    <div class="stats" id="statsSection">
      <!-- Stats will be populated by JavaScript -->
    </div>

    <div id="errorMessage"></div>
    <div id="loadingMessage" class="loading">🔄 Chargement des annonces...</div>
    <div id="announcementsContainer" class="announcements-grid">
      <!-- Announcements will be populated by JavaScript -->
    </div>
  </div>

  <script>
    let allAnnouncements = [];

    function showError(message) {
      document.getElementById('errorMessage').innerHTML = `<div class="error">❌ ${message}</div>`;
    }

    function hideError() {
      document.getElementById('errorMessage').innerHTML = '';
    }

    function showLoading(show = true) {
      document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
    }

    function updateStats(announcements) {
      const stats = {
        total: announcements.length,
        services: announcements.filter(a => a.categore === 'Service').length,
        materiel: announcements.filter(a => a.categore === 'Matériel').length,
        formations: announcements.filter(a => a.categore === 'Formation').length,
        disponibles: announcements.filter(a => a.etet === 'Disponible').length
      };

      document.getElementById('statsSection').innerHTML = `
        <div class="stat-card">
          <div class="stat-number">${stats.total}</div>
          <div class="stat-label">Total Annonces</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${stats.services}</div>
          <div class="stat-label">Services</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${stats.materiel}</div>
          <div class="stat-label">Matériel</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${stats.formations}</div>
          <div class="stat-label">Formations</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">${stats.disponibles}</div>
          <div class="stat-label">Disponibles</div>
        </div>
      `;
    }

    function displayAnnouncements(announcements) {
      const container = document.getElementById('announcementsContainer');
      
      if (announcements.length === 0) {
        container.innerHTML = `
          <div class="no-results">
            <h3>Aucune annonce trouvée</h3>
            <p>Aucune annonce ne correspond à vos critères de recherche.</p>
          </div>
        `;
        return;
      }

      container.innerHTML = announcements.map(announcement => {
        const statusClass = announcement.etet.toLowerCase().replace('é', 'e').replace(' ', '-');
        const imageUrl = announcement.image || 'photo2.PNG';
        
        return `
          <div class="announcement-card">
            <img src="${imageUrl}" alt="${announcement.titre}" class="announcement-image" onerror="this.style.display='none'">
            <div class="announcement-content">
              <div class="announcement-header">
                <div>
                  <h3 class="announcement-title">${announcement.titre}</h3>
                  <span class="announcement-category">${announcement.categore}</span>
                </div>
                <span class="announcement-status status-${statusClass}">${announcement.etet}</span>
              </div>
              
              <p class="announcement-description">${announcement.description}</p>
              
              <div class="announcement-footer">
                <div class="announcement-price">${announcement.prix.toLocaleString()} DA</div>
                <div>
                  <div class="announcement-user">👤 ${announcement.user_name} (${announcement.user_type})</div>
                  <div class="announcement-date">📅 ${new Date(announcement.date_posted).toLocaleDateString('fr-FR')}</div>
                </div>
              </div>
            </div>
          </div>
        `;
      }).join('');
    }

    async function loadAnnouncements(categore = '', etet = '') {
      try {
        hideError();
        showLoading(true);
        
        const params = new URLSearchParams();
        if (categore) params.append('categore', categore);
        if (etet) params.append('etet', etet);

        const url = `/api/announcements${params.toString() ? '?' + params.toString() : ''}`;
        console.log('Fetching announcements:', url);
        
        const response = await fetch(url);

        if (response.ok) {
          const announcements = await response.json();
          console.log('Announcements loaded:', announcements);
          
          allAnnouncements = announcements;
          displayAnnouncements(announcements);
          updateStats(announcements);
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error) {
        console.error('Error loading announcements:', error);
        showError(`Erreur lors du chargement des annonces: ${error.message}`);
        document.getElementById('announcementsContainer').innerHTML = '';
      } finally {
        showLoading(false);
      }
    }

    function applyFilters() {
      const categore = document.getElementById('categoryFilter').value;
      const etet = document.getElementById('statusFilter').value;
      console.log('Applying filters:', { categore, etet });
      loadAnnouncements(categore, etet);
    }

    function loadAllAnnouncements() {
      document.getElementById('categoryFilter').value = '';
      document.getElementById('statusFilter').value = '';
      loadAnnouncements();
    }

    // Auto-filter on select change
    document.getElementById('categoryFilter').addEventListener('change', applyFilters);
    document.getElementById('statusFilter').addEventListener('change', applyFilters);

    // Load all announcements on page load
    window.onload = function() {
      loadAllAnnouncements();
    };
  </script>

</body>
</html>
