<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Aphrovision - Connexion</title>

  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #ffffff;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      color: #333;
    }

    .top-bar {
      width: 100%;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #ffffff;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: bold;
      color: #333;
    }

    .nav-links {
      display: flex;
      gap: 15px;
    }

    .nav-links a {
      color: #333;
      text-decoration: none;
      padding: 8px 16px;
      border: 1px solid #333;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .nav-links a:hover {
      background-color: #629208;
      color: white;
      border-color: #629208;
    }

    .main-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 20px;
      background-color: #fff;
    }

    .content-box {
      display: flex;
      background-color: #f9f9f9;
      border-radius: 16px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      max-width: 900px;
      width: 100%;
      flex-wrap: wrap;
    }

    .image-side {
      flex: 1;
      min-width: 300px;
      max-height: 450px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .image-side img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .form-container {
      flex: 1;
      padding: 40px 30px;
      background-color: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    h2.title {
      text-align: center;
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      color: #000000;
      margin-bottom: 20px;
    }

    form {
      width: 100%;
    }

    input {
      width: 100%;
      padding: 10px;
      margin-top: 10px;
      border: 1px solid #ccc;
      border-radius: 6px;
      font-size: 0.95em;
    }

    input::placeholder {
      color: #999;
    }

    button {
      margin-top: 20px;
      width: 100%;
      padding: 12px;
      font-size: 1em;
      background-color: #000;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease, transform 0.2s ease;
    }

    button:hover {
      background-color: #629208;
      transform: translateY(-2px);
      box-shadow: 0 0 8px #ccc;
    }

    p {
      text-align: center;
      margin-top: 15px;
      font-size: 0.85em;
    }

    a {
      color:#629208;
      text-decoration: none;
      font-weight: bold;
    }

    a:hover {
      text-decoration: underline;
    }

    @media screen and (max-width: 768px) {
      .content-box {
        flex-direction: column;
      }

      .image-side {
        height: 200px;
        max-height: none;
      }

      .form-container {
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>

  <!-- Barre de navigation -->
  <div class="top-bar">
    <div class="logo">Aphrovision</div>
    <div class="nav-links">
      <a href="login.html">Login</a>
      <a href="signup.html">Signup</a>
    </div>
  </div>

  <!-- Contenu principal -->
  <div class="main-container">
    <div class="content-box">

      <!-- Image -->
      <div class="image-side">
        <img src="photos2.jpg" alt="illustration connexion">
      </div>

      <!-- Formulaire -->
      <div class="form-container">
        <h2 class="title">Connexion</h2>
        <div id="error-message" style="color: red; text-align: center; margin-bottom: 10px; display: none;"></div>
        <div id="success-message" style="color: green; text-align: center; margin-bottom: 10px; display: none;"></div>
        <form id="loginForm">
          <input type="text" id="username" placeholder="Nom d'utilisateur" required>
          <input type="password" id="password" placeholder="Mot de passe" required>
          <button type="submit">Se connecter</button>
        </form>
        <p>Vous n'avez pas de compte ? <a href="signup.html">S'inscrire</a></p>
      </div>

    </div>
  </div>

  <script>
    document.getElementById('loginForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const errorDiv = document.getElementById('error-message');
      const successDiv = document.getElementById('success-message');

      // Hide previous messages
      errorDiv.style.display = 'none';
      successDiv.style.display = 'none';

      try {
        const response = await fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: username,
            password: password
          })
        });

        const data = await response.json();

        if (response.ok) {
          // Success
          successDiv.textContent = 'Connexion réussie! Redirection...';
          successDiv.style.display = 'block';

          // Store user info in localStorage
          localStorage.setItem('user', JSON.stringify(data.user));

          // Redirect after 1 second
          setTimeout(() => {
            window.location.href = 'accueil.html';
          }, 1000);
        } else {
          // Error
          errorDiv.textContent = data.error || 'Erreur de connexion';
          errorDiv.style.display = 'block';
        }
      } catch (error) {
        console.error('Error:', error);
        errorDiv.textContent = 'Erreur de connexion au serveur';
        errorDiv.style.display = 'block';
      }
    });
  </script>

</body>
</html>
