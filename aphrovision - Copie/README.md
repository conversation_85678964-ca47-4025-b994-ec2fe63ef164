# 📸 Aphrovision - Photography Platform

A modern web platform connecting photographers with clients in Algeria.

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Test Database Connection
```bash
python test_db.py
```

### 3. Start the Server
```bash
python run_server.py
```

### 4. Open Your Browser
Navigate to: `http://localhost:5000`

## 📁 Project Structure

```
aphrovision/
├── app.py                 # Main Flask application
├── run_server.py         # Server startup script
├── test_db.py           # Database test script
├── requirements.txt     # Python dependencies
├── config.py           # Configuration settings
├── database_mysql.sql  # MySQL schema for phpMyAdmin
├── photographe_frelance.db  # SQLite database (auto-created)
├── static/
│   └── uploads/        # Uploaded images
├── login.html          # Login page
├── signup.html         # Registration page
├── poster.html         # Photo upload page
├── photographers.html  # Photographer listing
└── [other HTML files]
```

## 🔧 Features Implemented

### ✅ Backend (Flask)
- User authentication (login/register)
- Photo upload and storage
- Photographer profiles
- Client profiles
- RESTful API endpoints
- SQLite database with proper schema
- File upload handling
- Session management

### ✅ Frontend Integration
- Login form connected to backend
- Registration form with user type selection
- Photo upload with metadata
- Photographer listing from database
- Real-time form validation
- Error handling and user feedback

### ✅ Database Schema
- Users table (authentication)
- Client and Photographer profiles
- Photos with metadata
- Projects and announcements
- Messages system
- Proper foreign key relationships

## 🔑 Test Accounts

After running `python test_db.py`, you can use these accounts:

| Username | Password | Type | Description |
|----------|----------|------|-------------|
| admin | password123 | Photographer | Admin account |
| client_test | password123 | Client | Test client |
| photo_sophie | password123 | Photographer | Test photographer |

## 🌐 API Endpoints

### Authentication
- `POST /api/register` - User registration
- `POST /api/login` - User login
- `POST /api/logout` - User logout

### Photos
- `GET /api/photos` - Get all public photos
- `POST /api/photos/upload` - Upload new photo (photographers only)

### Photographers
- `GET /api/photographers` - Get all photographers
- `GET /api/photographers?domaine=Portrait&wilaya=Alger` - Filter photographers

### Messages
- `GET /api/messages` - Get user messages
- `POST /api/messages` - Send message

## 🗄️ Database Options

### SQLite (Development)
The project uses SQLite by default for easy development. The database file `photographe_frelance.db` is created automatically.

### MySQL (Production)
For production with phpMyAdmin:
1. Copy the contents of `database_mysql.sql`
2. Open phpMyAdmin
3. Create a new database called `aphrovision`
4. Go to SQL tab and paste the script
5. Execute the script

## 🔧 Configuration

Edit `config.py` or create a `.env` file (copy from `.env.example`) to configure:
- Database settings
- Secret keys
- Upload limits
- CORS settings

## 🐛 Troubleshooting

### Database Issues
```bash
# Test database connection
python test_db.py

# Check if tables exist
sqlite3 photographe_frelance.db ".tables"
```

### Server Issues
```bash
# Check if port 5000 is available
lsof -i :5000

# Run with different port
python -c "from app import app; app.run(port=5001)"
```

### Upload Issues
- Ensure `static/uploads/` directory exists
- Check file permissions
- Verify file size limits in config

## 📝 Usage Instructions

1. **Register**: Go to signup.html and create an account
2. **Login**: Use login.html with your credentials
3. **Upload Photos**: Photographers can use poster.html
4. **Browse**: View photographers on photographers.html
5. **Filter**: Use domain and wilaya filters to find specific photographers

## 🔄 Development Workflow

1. Make changes to Python files
2. Server auto-reloads (debug mode)
3. Test in browser
4. Check console for errors
5. Use browser dev tools for frontend debugging

## 📞 Support

If you encounter issues:
1. Check the console output
2. Verify database connection with `test_db.py`
3. Ensure all dependencies are installed
4. Check file permissions for uploads

## 🎯 Next Steps

- Add more photo management features
- Implement messaging system
- Add payment integration
- Create mobile-responsive design
- Add photo editing capabilities
- Implement search functionality
