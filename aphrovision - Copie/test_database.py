#!/usr/bin/env python3
"""
Test script to verify database setup and operations
"""
import sqlite3
import sys
import os
from database_utils import DatabaseManager, get_database_stats

def test_database_connection():
    """Test basic database connection"""
    print("Testing database connection...")
    db = DatabaseManager()
    conn = db.get_connection()
    
    if conn:
        print("✅ Database connection successful")
        conn.close()
        return True
    else:
        print("❌ Database connection failed")
        return False

def test_tables_exist():
    """Test if all required tables exist"""
    print("\nTesting table existence...")
    db = DatabaseManager()
    
    required_tables = ['client', 'photographe', 'projet', 'photo', 'annonce']
    all_exist = True
    
    for table in required_tables:
        if db.table_exists(table):
            print(f"✅ Table '{table}' exists")
        else:
            print(f"❌ Table '{table}' does not exist")
            all_exist = False
    
    return all_exist

def test_database_operations():
    """Test basic CRUD operations"""
    print("\nTesting database operations...")
    db = DatabaseManager()
    
    # Test SELECT operation
    try:
        clients = db.execute_query("SELECT COUNT(*) as count FROM client")
        if clients:
            print(f"✅ SELECT operation successful - {clients[0]['count']} clients found")
        else:
            print("❌ SELECT operation failed")
            return False
    except Exception as e:
        print(f"❌ SELECT operation error: {e}")
        return False
    
    # Test INSERT operation (we'll insert and then delete)
    try:
        success = db.execute_update(
            "INSERT INTO client (nom, prenom, email, phone) VALUES (?, ?, ?, ?)",
            ("Test", "User", "<EMAIL>", "1234567890")
        )
        if success:
            print("✅ INSERT operation successful")
        else:
            print("❌ INSERT operation failed")
            return False
    except Exception as e:
        print(f"❌ INSERT operation error: {e}")
        return False
    
    # Test DELETE operation (clean up test data)
    try:
        success = db.execute_update(
            "DELETE FROM client WHERE email = ?",
            ("<EMAIL>",)
        )
        if success:
            print("✅ DELETE operation successful")
        else:
            print("❌ DELETE operation failed")
    except Exception as e:
        print(f"❌ DELETE operation error: {e}")
    
    return True

def show_database_stats():
    """Show database statistics"""
    print("\nDatabase Statistics:")
    print("-" * 30)
    
    try:
        stats = get_database_stats()
        for table, count in stats.items():
            print(f"{table.capitalize()}: {count} records")
    except Exception as e:
        print(f"Error getting stats: {e}")

def show_table_schemas():
    """Show table schemas"""
    print("\nTable Schemas:")
    print("-" * 30)
    
    db = DatabaseManager()
    tables = ['client', 'photographe', 'projet', 'photo', 'annonce']
    
    for table in tables:
        print(f"\n{table.upper()} table:")
        schema = db.get_table_info(table)
        if schema:
            for column in schema:
                print(f"  - {column['name']}: {column['type']} {'(PK)' if column['pk'] else ''}")
        else:
            print(f"  Could not get schema for {table}")

def main():
    """Main test function"""
    print("🔍 Database Setup Test")
    print("=" * 50)
    
    # Check if database file exists
    db_file = 'photographe_frelance.sqlite'
    if os.path.exists(db_file):
        print(f"✅ Database file '{db_file}' exists")
    else:
        print(f"❌ Database file '{db_file}' not found")
        return
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    if test_database_connection():
        tests_passed += 1
    
    if test_tables_exist():
        tests_passed += 1
    
    if test_database_operations():
        tests_passed += 1
    
    # Show additional info
    show_database_stats()
    show_table_schemas()
    
    # Summary
    print("\n" + "=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Your database setup is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
