from flask import Flask, render_template, jsonify, request, redirect, url_for, session, flash, send_from_directory
from flask_cors import CORS
import sqlite3
import os
from datetime import datetime
from config import config
from database_utils import DatabaseManager
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash, check_password_hash
import uuid

# Create Flask app
app = Flask(__name__, static_folder='.', static_url_path='')

# Load configuration
config_name = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[config_name])

# Initialize CORS
CORS(app, origins=app.config['CORS_ORIGINS'])

# Database setup
DATABASE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'photographe_frelance.sqlite')
db_manager = DatabaseManager(DATABASE)

# Upload configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_db_connection():
    """Get database connection with error handling"""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        # Enable foreign key constraints
        conn.execute('PRAGMA foreign_keys = ON')
        return conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}")
        return None

# Création des tables si elles n'existent pas
def init_db():
    """Initialize database tables"""
    conn = get_db_connection()
    if conn is None:
        print("Failed to connect to database")
        return False

    try:
        cursor = conn.cursor()

        # Create users table for authentication
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                user_type TEXT NOT NULL CHECK (user_type IN ('client', 'photographe')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS photographe (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT NOT NULL,
                wilaya TEXT NOT NULL,
                domaine TEXT NOT NULL,
                bio TEXT NOT NULL,
                profile_image TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Add profile_image column if it doesn't exist (for existing databases)
        try:
            cursor.execute('ALTER TABLE photographe ADD COLUMN profile_image TEXT')
        except sqlite3.OperationalError:
            # Column already exists
            pass

        # Add user_id column to client table if it doesn't exist
        try:
            cursor.execute('ALTER TABLE client ADD COLUMN user_id INTEGER')
        except sqlite3.OperationalError:
            # Column already exists
            pass

        # Add user_id column to photographe table if it doesn't exist
        try:
            cursor.execute('ALTER TABLE photographe ADD COLUMN user_id INTEGER')
        except sqlite3.OperationalError:
            # Column already exists
            pass
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projet (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                id_client INTEGER NOT NULL,
                id_photographe INTEGER NOT NULL,
                description TEXT NOT NULL,
                date_posted TEXT NOT NULL,
                image TEXT,
                FOREIGN KEY (id_client) REFERENCES client (id),
                FOREIGN KEY (id_photographe) REFERENCES photographe (id)
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS photo (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                id_photographe INTEGER NOT NULL,
                image TEXT NOT NULL,
                description TEXT NOT NULL,
                FOREIGN KEY (id_photographe) REFERENCES photographe (id)
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS annonce (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                categore TEXT NOT NULL,
                id_utilisateur INTEGER NOT NULL,
                etet TEXT NOT NULL,
                image TEXT,
                titre TEXT NOT NULL,
                description TEXT NOT NULL,
                prix REAL NOT NULL,
                date_posted TEXT NOT NULL
            )
        ''')
        conn.commit()
        conn.close()
        print("Database initialized successfully")
        return True
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
        if conn:
            conn.close()
        return False

init_db()

# --- ROUTES CLIENT ---

@app.route('/clients')
def clients():
    conn = get_db_connection()
    clients = conn.execute('SELECT * FROM client').fetchall()
    conn.close()
    return render_template('client_list.html', clients=clients)

@app.route('/clients/add', methods=('GET', 'POST'))
def add_client():
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']

        # Get user_id from session if available
        user_id = session.get('user_id')

        conn = get_db_connection()
        conn.execute('INSERT INTO client (user_id, nom, prenom, email, phone) VALUES (?, ?, ?, ?, ?)',
                     (user_id, nom, prenom, email, phone))
        conn.commit()
        conn.close()

        if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
            return jsonify({'message': 'Client créé avec succès'}), 201
        return redirect(url_for('clients'))
    return render_template('client_form.html', client=None)

@app.route('/clients/edit/<int:id>', methods=('GET', 'POST'))
def edit_client(id):
    conn = get_db_connection()
    client = conn.execute('SELECT * FROM client WHERE id = ?', (id,)).fetchone()
    if client is None:
        conn.close()
        return "Client non trouvé", 404
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']

        conn.execute('UPDATE client SET nom = ?, prenom = ?, email = ?, phone = ? WHERE id = ?',
                     (nom, prenom, email, phone, id))
        conn.commit()
        conn.close()
        return redirect(url_for('clients'))
    conn.close()
    return render_template('client_form.html', client=client)

@app.route('/clients/delete/<int:id>', methods=('POST',))
def delete_client(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM client WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('clients'))

# --- ROUTES PHOTOGRAPHE ---

@app.route('/photographes')
def photographes():
    conn = get_db_connection()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('photographe_list.html', photographes=photographes)

@app.route('/photographes/add', methods=('GET', 'POST'))
def add_photographe():
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']
        wilaya = request.form['wilaya']
        domaine = request.form['domaine']
        bio = request.form['bio']

        # Get user_id from session if available
        user_id = session.get('user_id')

        conn = get_db_connection()
        conn.execute('INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                     (user_id, nom, prenom, email, phone, wilaya, domaine, bio))
        conn.commit()
        conn.close()

        if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
            return jsonify({'message': 'Photographe créé avec succès'}), 201
        return redirect(url_for('photographes'))
    return render_template('photographe_form.html', photographe=None)

@app.route('/photographes/edit/<int:id>', methods=('GET', 'POST'))
def edit_photographe(id):
    conn = get_db_connection()
    photographe = conn.execute('SELECT * FROM photographe WHERE id = ?', (id,)).fetchone()
    if photographe is None:
        conn.close()
        return "Photographe non trouvé", 404
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']
        wilaya = request.form['wilaya']
        domaine = request.form['domaine']
        bio = request.form['bio']

        conn.execute('''
            UPDATE photographe SET nom=?, prenom=?, email=?, phone=?, wilaya=?, domaine=?, bio=?
            WHERE id=?
        ''', (nom, prenom, email, phone, wilaya, domaine, bio, id))
        conn.commit()
        conn.close()
        return redirect(url_for('photographes'))
    conn.close()
    return render_template('photographe_form.html', photographe=photographe)

@app.route('/photographes/delete/<int:id>', methods=('POST',))
def delete_photographe(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM photographe WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('photographes'))

# --- ROUTES PROJET ---

@app.route('/projets')
def projets():
    conn = get_db_connection()
    projets = conn.execute('SELECT * FROM projet').fetchall()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('projet_list.html', projets=projets, clients=clients, photographes=photographes)

@app.route('/projets/add', methods=('GET', 'POST'))
def add_projet():
    conn = get_db_connection()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()

    if request.method == 'POST':
        id_client = request.form['id_client']
        id_photographe = request.form['id_photographe']
        description = request.form['description']
        date_posted = request.form['date_posted']
        image = request.form['image']

        conn = get_db_connection()
        conn.execute('INSERT INTO projet (id_client, id_photographe, description, date_posted, image) VALUES (?, ?, ?, ?, ?)',
                     (id_client, id_photographe, description, date_posted, image))
        conn.commit()
        conn.close()
        return redirect(url_for('projets'))
    return render_template('projet_form.html', projet=None, clients=clients, photographes=photographes)

@app.route('/projets/edit/<int:id>', methods=('GET', 'POST'))
def edit_projet(id):
    conn = get_db_connection()
    projet = conn.execute('SELECT * FROM projet WHERE id = ?', (id,)).fetchone()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    if projet is None:
        conn.close()
        return "Projet non trouvé", 404
    if request.method == 'POST':
        id_client = request.form['id_client']
        id_photographe = request.form['id_photographe']
        description = request.form['description']
        date_posted = request.form['date_posted']
        image = request.form['image']

        conn.execute('''
            UPDATE projet SET id_client=?, id_photographe=?, description=?, date_posted=?, image=?
            WHERE id=?
        ''', (id_client, id_photographe, description, date_posted, image, id))
        conn.commit()
        conn.close()
        return redirect(url_for('projets'))
    conn.close()
    return render_template('projet_form.html', projet=projet, clients=clients, photographes=photographes)

@app.route('/projets/delete/<int:id>', methods=('POST',))
def delete_projet(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM projet WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('projets'))

# --- ROUTES PHOTO ---

@app.route('/photos')
def photos():
    conn = get_db_connection()
    photos = conn.execute('SELECT * FROM photo').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('photo_list.html', photos=photos, photographes=photographes)

@app.route('/photos/add', methods=('GET', 'POST'))
def add_photo():
    conn = get_db_connection()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    if request.method == 'POST':
        id_photographe = request.form['id_photographe']
        image = request.form['image']
        description = request.form['description']

        conn = get_db_connection()
        conn.execute('INSERT INTO photo (id_photographe, image, description) VALUES (?, ?, ?)',
                     (id_photographe, image, description))
        conn.commit()
        conn.close()
        return redirect(url_for('photos'))
    return render_template('photo_form.html', photo=None, photographes=photographes)

@app.route('/photos/edit/<int:id>', methods=('GET', 'POST'))
def edit_photo(id):
    conn = get_db_connection()
    photo = conn.execute('SELECT * FROM photo WHERE id = ?', (id,)).fetchone()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    if photo is None:
        conn.close()
        return "Photo non trouvée", 404
    if request.method == 'POST':
        id_photographe = request.form['id_photographe']
        image = request.form['image']
        description = request.form['description']

        conn.execute('UPDATE photo SET id_photographe=?, image=?, description=? WHERE id=?',
                     (id_photographe, image, description, id))
        conn.commit()
        conn.close()
        return redirect(url_for('photos'))
    conn.close()
    return render_template('photo_form.html', photo=photo, photographes=photographes)

@app.route('/photos/delete/<int:id>', methods=('POST',))
def delete_photo(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM photo WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('photos'))

# --- ROUTES ANNONCE ---

@app.route('/annonces')
def annonces():
    conn = get_db_connection()
    annonces = conn.execute('SELECT * FROM annonce').fetchall()
    conn.close()
    return render_template('annonce_list.html', annonces=annonces)

@app.route('/annonces/add', methods=('GET', 'POST'))
def add_annonce():
    if request.method == 'POST':
        categore = request.form['categore']
        id_utilisateur = request.form['id_utilisateur']
        etet = request.form['etet']
        image = request.form['image']
        titre = request.form['titre']
        description = request.form['description']
        prix = request.form['prix']
        date_posted = request.form['date_posted']

        conn = get_db_connection()
        conn.execute('''
            INSERT INTO annonce (categore, id_utilisateur, etet, image, titre, description, prix, date_posted)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (categore, id_utilisateur, etet, image, titre, description, prix, date_posted))
        conn.commit()
        conn.close()
        return redirect(url_for('annonces'))
    return render_template('annonce_form.html', annonce=None)

@app.route('/annonces/edit/<int:id>', methods=('GET', 'POST'))
def edit_annonce(id):
    conn = get_db_connection()
    annonce = conn.execute('SELECT * FROM annonce WHERE id = ?', (id,)).fetchone()
    if annonce is None:
        conn.close()
        return "Annonce non trouvée", 404
    if request.method == 'POST':
        categore = request.form['categore']
        id_utilisateur = request.form['id_utilisateur']
        etet = request.form['etet']
        image = request.form['image']
        titre = request.form['titre']
        description = request.form['description']
        prix = request.form['prix']
        date_posted = request.form['date_posted']

        conn.execute('''
            UPDATE annonce SET categore=?, id_utilisateur=?, etet=?, image=?, titre=?, description=?, prix=?, date_posted=?
            WHERE id=?
        ''', (categore, id_utilisateur, etet, image, titre, description, prix, date_posted, id))
        conn.commit()
        conn.close()
        return redirect(url_for('annonces'))
    conn.close()
    return render_template('annonce_form.html', annonce=annonce)

@app.route('/annonces/delete/<int:id>', methods=('POST',))
def delete_annonce(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM annonce WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('annonces'))

@app.route('/api/photographers', methods=['GET'])
def api_photographers():
    domaine = request.args.get('domaine', '')
    wilaya = request.args.get('wilaya', '')

    conn = get_db_connection()
    if conn is None:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        # Return data in the format expected by your JavaScript (nom, prenom, domaine, wilaya)
        query = "SELECT nom, prenom, domaine, wilaya, bio, email, phone, profile_image FROM photographe WHERE 1=1"
        params = []
        if domaine:
            query += " AND domaine = ?"
            params.append(domaine)
        if wilaya:
            query += " AND wilaya = ?"
            params.append(wilaya)

        photographers = conn.execute(query, params).fetchall()
        conn.close()
    except sqlite3.Error as e:
        if conn:
            conn.close()
        return jsonify({'error': f'Database query failed: {e}'}), 500

    # Transformer en liste de dicts
    results = [dict(row) for row in photographers]
    return jsonify(results)

# --- IMAGE UPLOAD API ---

@app.route('/api/upload-image', methods=['POST'])
def upload_image():
    """Upload image for photographer profile"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']
    photographer_id = request.form.get('photographer_id')

    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if not photographer_id:
        return jsonify({'error': 'Photographer ID required'}), 400

    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"{uuid.uuid4()}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        try:
            file.save(filepath)

            # Update photographer record with image filename
            conn = get_db_connection()
            if conn:
                conn.execute(
                    "UPDATE photographe SET profile_image = ? WHERE id = ?",
                    (unique_filename, photographer_id)
                )
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'filename': unique_filename,
                    'message': 'Image uploaded successfully'
                })
            else:
                return jsonify({'error': 'Database connection failed'}), 500

        except Exception as e:
            return jsonify({'error': f'Upload failed: {str(e)}'}), 500

    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/api/photographer/<int:photographer_id>/upload', methods=['POST'])
def upload_photographer_image(photographer_id):
    """Upload image for specific photographer"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        unique_filename = f"photographer_{photographer_id}_{uuid.uuid4()}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)

        try:
            file.save(filepath)

            # Update photographer record with image filename
            conn = get_db_connection()
            if conn:
                conn.execute(
                    "UPDATE photographe SET profile_image = ? WHERE id = ?",
                    (unique_filename, photographer_id)
                )
                conn.commit()
                conn.close()

                return jsonify({
                    'success': True,
                    'filename': unique_filename,
                    'url': f'/uploads/{unique_filename}',
                    'message': 'Image uploaded successfully'
                })
            else:
                return jsonify({'error': 'Database connection failed'}), 500

        except Exception as e:
            return jsonify({'error': f'Upload failed: {str(e)}'}), 500

    return jsonify({'error': 'Invalid file type. Allowed: PNG, JPG, JPEG, GIF'}), 400

# --- AUTHENTICATION API ---

@app.route('/api/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['username', 'email', 'password', 'user_type']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        username = data['username']
        email = data['email']
        password = data['password']
        user_type = data['user_type']

        # Validate user_type
        if user_type not in ['client', 'photographe']:
            return jsonify({'error': 'Invalid user type'}), 400

        # Hash password
        password_hash = generate_password_hash(password)

        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'Database connection failed'}), 500

        try:
            # Check if username or email already exists
            existing_user = conn.execute(
                'SELECT id FROM users WHERE username = ? OR email = ?',
                (username, email)
            ).fetchone()

            if existing_user:
                return jsonify({'error': 'Username or email already exists'}), 400

            # Create user
            cursor = conn.execute(
                'INSERT INTO users (username, email, password_hash, user_type) VALUES (?, ?, ?, ?)',
                (username, email, password_hash, user_type)
            )
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return jsonify({
                'message': 'User registered successfully',
                'user_id': user_id,
                'username': username,
                'user_type': user_type
            }), 201

        except sqlite3.IntegrityError as e:
            conn.close()
            return jsonify({'error': 'Username or email already exists'}), 400
        except sqlite3.Error as e:
            conn.close()
            return jsonify({'error': f'Database error: {str(e)}'}), 500

    except Exception as e:
        return jsonify({'error': f'Registration failed: {str(e)}'}), 500

@app.route('/api/login', methods=['POST'])
def login():
    """Login user"""
    try:
        data = request.get_json()

        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return jsonify({'error': 'Username and password are required'}), 400

        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'Database connection failed'}), 500

        # Get user by username
        user = conn.execute(
            'SELECT id, username, email, password_hash, user_type FROM users WHERE username = ?',
            (username,)
        ).fetchone()

        if user and check_password_hash(user['password_hash'], password):
            # Store user info in session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['user_type'] = user['user_type']

            conn.close()

            return jsonify({
                'message': 'Login successful',
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'user_type': user['user_type']
                }
            }), 200
        else:
            conn.close()
            return jsonify({'error': 'Invalid username or password'}), 401

    except Exception as e:
        return jsonify({'error': f'Login failed: {str(e)}'}), 500

@app.route('/api/logout', methods=['POST'])
def logout():
    """Logout user"""
    session.clear()
    return jsonify({'message': 'Logged out successfully'}), 200

@app.route('/api/register-complete', methods=['POST'])
def register_complete():
    """Register user and create profile in one step"""
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['username', 'email', 'password', 'user_type', 'nom', 'prenom', 'phone']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        username = data['username']
        email = data['email']
        password = data['password']
        user_type = data['user_type']
        nom = data['nom']
        prenom = data['prenom']
        phone = data['phone']

        # Validate user_type
        if user_type not in ['client', 'photographe']:
            return jsonify({'error': 'Invalid user type'}), 400

        # For photographers, validate additional fields
        if user_type == 'photographe':
            required_photo_fields = ['wilaya', 'domaine', 'bio']
            for field in required_photo_fields:
                if not data.get(field):
                    return jsonify({'error': f'{field} is required for photographers'}), 400

        # Hash password
        password_hash = generate_password_hash(password)

        conn = get_db_connection()
        if conn is None:
            return jsonify({'error': 'Database connection failed'}), 500

        try:
            # Check if username or email already exists
            existing_user = conn.execute(
                'SELECT id FROM users WHERE username = ? OR email = ?',
                (username, email)
            ).fetchone()

            if existing_user:
                conn.close()
                return jsonify({'error': 'Username or email already exists'}), 400

            # Create user
            cursor = conn.execute(
                'INSERT INTO users (username, email, password_hash, user_type) VALUES (?, ?, ?, ?)',
                (username, email, password_hash, user_type)
            )
            user_id = cursor.lastrowid

            # Create profile based on user type
            if user_type == 'client':
                conn.execute(
                    'INSERT INTO client (user_id, nom, prenom, email, phone) VALUES (?, ?, ?, ?, ?)',
                    (user_id, nom, prenom, email, phone)
                )
            elif user_type == 'photographe':
                wilaya = data['wilaya']
                domaine = data['domaine']
                bio = data['bio']
                conn.execute(
                    'INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                    (user_id, nom, prenom, email, phone, wilaya, domaine, bio)
                )

            conn.commit()
            conn.close()

            return jsonify({
                'message': 'User and profile created successfully',
                'user_id': user_id,
                'username': username,
                'user_type': user_type
            }), 201

        except sqlite3.IntegrityError as e:
            conn.close()
            return jsonify({'error': 'Username or email already exists'}), 400
        except sqlite3.Error as e:
            conn.close()
            return jsonify({'error': f'Database error: {str(e)}'}), 500

    except Exception as e:
        return jsonify({'error': f'Registration failed: {str(e)}'}), 500

# --- ANNOUNCEMENTS API ---

@app.route('/api/announcements', methods=['GET'])
def get_announcements():
    """Get all announcements with optional filtering"""
    categore = request.args.get('categore', '')
    etet = request.args.get('etet', '')

    conn = get_db_connection()
    if conn is None:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        # Get announcements with user information
        query = """
        SELECT a.id, a.categore, a.id_utilisateur, a.etet, a.image, a.titre,
               a.description, a.prix, a.date_posted,
               CASE
                   WHEN a.id_utilisateur IN (SELECT id FROM client) THEN
                       (SELECT nom || ' ' || prenom FROM client WHERE id = a.id_utilisateur)
                   WHEN a.id_utilisateur IN (SELECT id FROM photographe) THEN
                       (SELECT nom || ' ' || prenom FROM photographe WHERE id = a.id_utilisateur)
                   ELSE 'Utilisateur Inconnu'
               END as user_name,
               CASE
                   WHEN a.id_utilisateur IN (SELECT id FROM client) THEN 'Client'
                   WHEN a.id_utilisateur IN (SELECT id FROM photographe) THEN 'Photographe'
                   ELSE 'Inconnu'
               END as user_type
        FROM annonce a
        WHERE 1=1
        """
        params = []

        if categore:
            query += " AND a.categore = ?"
            params.append(categore)
        if etet:
            query += " AND a.etet = ?"
            params.append(etet)

        query += " ORDER BY a.date_posted DESC"

        announcements = conn.execute(query, params).fetchall()
        conn.close()

        # Transform to list of dicts
        results = [dict(row) for row in announcements]
        return jsonify(results)

    except sqlite3.Error as e:
        if conn:
            conn.close()
        return jsonify({'error': f'Database query failed: {e}'}), 500

@app.route('/api/announcements/<int:announcement_id>', methods=['GET'])
def get_announcement(announcement_id):
    """Get specific announcement by ID"""
    conn = get_db_connection()
    if conn is None:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        query = """
        SELECT a.id, a.categore, a.id_utilisateur, a.etet, a.image, a.titre,
               a.description, a.prix, a.date_posted,
               CASE
                   WHEN a.id_utilisateur IN (SELECT id FROM client) THEN
                       (SELECT nom || ' ' || prenom FROM client WHERE id = a.id_utilisateur)
                   WHEN a.id_utilisateur IN (SELECT id FROM photographe) THEN
                       (SELECT nom || ' ' || prenom FROM photographe WHERE id = a.id_utilisateur)
                   ELSE 'Utilisateur Inconnu'
               END as user_name,
               CASE
                   WHEN a.id_utilisateur IN (SELECT id FROM client) THEN 'Client'
                   WHEN a.id_utilisateur IN (SELECT id FROM photographe) THEN 'Photographe'
                   ELSE 'Inconnu'
               END as user_type
        FROM annonce a
        WHERE a.id = ?
        """

        announcement = conn.execute(query, (announcement_id,)).fetchone()
        conn.close()

        if announcement:
            return jsonify(dict(announcement))
        else:
            return jsonify({'error': 'Announcement not found'}), 404

    except sqlite3.Error as e:
        if conn:
            conn.close()
        return jsonify({'error': f'Database query failed: {e}'}), 500


# --- ROUTE ACCUEIL ---

@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

# --- ROUTES FOR YOUR EXISTING HTML PAGES ---

@app.route('/accueil.html')
def accueil():
    return send_from_directory('.', 'accueil.html')

@app.route('/explore.html')
def explore():
    return send_from_directory('.', 'explore.html')

@app.route('/login.html')
def login_page():
    return send_from_directory('.', 'login.html')

@app.route('/signup.html')
def signup():
    return send_from_directory('.', 'signup.html')

@app.route('/photographers.html')
def photographers_page():
    return send_from_directory('.', 'photographers.html')

@app.route('/profile.html')
def profile():
    return send_from_directory('.', 'profile.html')

@app.route('/profile_view.html')
def profile_view():
    return send_from_directory('.', 'profile_view.html')

@app.route('/poster.html')
def poster():
    return send_from_directory('.', 'poster.html')

@app.route('/shop.html')
def shop():
    return send_from_directory('.', 'shop.html')

@app.route('/product.html')
def product():
    return send_from_directory('.', 'product.html')

@app.route('/editor.html')
def editor():
    return send_from_directory('.', 'editor.html')

@app.route('/test_api.html')
def test_api():
    return send_from_directory('.', 'test_api.html')

@app.route('/test_features.html')
def test_features():
    return send_from_directory('.', 'test_features.html')

@app.route('/annonces.html')
def annonces_page():
    return send_from_directory('.', 'annonces.html')

# --- STATIC FILES ---

@app.route('/styles.css')
def styles():
    return send_from_directory('.', 'styles.css')

@app.route('/script.js')
def script():
    return send_from_directory('.', 'script.js')

# --- UPLOADED IMAGES ---

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# --- IMAGE FILES ---

@app.route('/<path:filename>')
def serve_static(filename):
    # Serve image files and other static assets
    if filename.endswith(('.png', '.jpg', '.jpeg', '.gif', '.PNG', '.JPG', '.JPEG', '.GIF')):
        return send_from_directory('.', filename)
    return "File not found", 404

if __name__ == '__main__':
    app.run(debug=True)
