from flask import Flask, render_template, jsonify, request, redirect, url_for, session, flash
from flask_cors import CORS
import sqlite3
import os
from datetime import datetime
from config import config
from database_utils import DatabaseManager

# Create Flask app
app = Flask(__name__)

# Load configuration
config_name = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[config_name])

# Initialize CORS
CORS(app, origins=app.config['CORS_ORIGINS'])

# Database setup
DATABASE = app.config['DATABASE_URL']
db_manager = DatabaseManager(DATABASE)

def get_db_connection():
    """Get database connection with error handling"""
    try:
        conn = sqlite3.connect(DATABASE)
        conn.row_factory = sqlite3.Row
        # Enable foreign key constraints
        conn.execute('PRAGMA foreign_keys = ON')
        return conn
    except sqlite3.Error as e:
        print(f"Database connection error: {e}")
        return None

# Création des tables si elles n'existent pas
def init_db():
    """Initialize database tables"""
    conn = get_db_connection()
    if conn is None:
        print("Failed to connect to database")
        return False

    try:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT NOT NULL
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS photographe (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                nom TEXT NOT NULL,
                prenom TEXT NOT NULL,
                email TEXT NOT NULL,
                phone TEXT NOT NULL,
                wilaya TEXT NOT NULL,
                domaine TEXT NOT NULL,
                bio TEXT NOT NULL
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS projet (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                id_client INTEGER NOT NULL,
                id_photographe INTEGER NOT NULL,
                description TEXT NOT NULL,
                date_posted TEXT NOT NULL,
                image TEXT,
                FOREIGN KEY (id_client) REFERENCES client (id),
                FOREIGN KEY (id_photographe) REFERENCES photographe (id)
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS photo (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                id_photographe INTEGER NOT NULL,
                image TEXT NOT NULL,
                description TEXT NOT NULL,
                FOREIGN KEY (id_photographe) REFERENCES photographe (id)
            )
        ''')
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS annonce (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                categore TEXT NOT NULL,
                id_utilisateur INTEGER NOT NULL,
                etet TEXT NOT NULL,
                image TEXT,
                titre TEXT NOT NULL,
                description TEXT NOT NULL,
                prix REAL NOT NULL,
                date_posted TEXT NOT NULL
            )
        ''')
        conn.commit()
        conn.close()
        print("Database initialized successfully")
        return True
    except sqlite3.Error as e:
        print(f"Database initialization error: {e}")
        if conn:
            conn.close()
        return False

init_db()

# --- ROUTES CLIENT ---

@app.route('/clients')
def clients():
    conn = get_db_connection()
    clients = conn.execute('SELECT * FROM client').fetchall()
    conn.close()
    return render_template('client_list.html', clients=clients)

@app.route('/clients/add', methods=('GET', 'POST'))
def add_client():
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']

        # Get user_id from session if available
        user_id = session.get('user_id')

        conn = get_db_connection()
        conn.execute('INSERT INTO client (user_id, nom, prenom, email, phone) VALUES (?, ?, ?, ?, ?)',
                     (user_id, nom, prenom, email, phone))
        conn.commit()
        conn.close()

        if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
            return jsonify({'message': 'Client créé avec succès'}), 201
        return redirect(url_for('clients'))
    return render_template('client_form.html', client=None)

@app.route('/clients/edit/<int:id>', methods=('GET', 'POST'))
def edit_client(id):
    conn = get_db_connection()
    client = conn.execute('SELECT * FROM client WHERE id = ?', (id,)).fetchone()
    if client is None:
        conn.close()
        return "Client non trouvé", 404
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']

        conn.execute('UPDATE client SET nom = ?, prenom = ?, email = ?, phone = ? WHERE id = ?',
                     (nom, prenom, email, phone, id))
        conn.commit()
        conn.close()
        return redirect(url_for('clients'))
    conn.close()
    return render_template('client_form.html', client=client)

@app.route('/clients/delete/<int:id>', methods=('POST',))
def delete_client(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM client WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('clients'))

# --- ROUTES PHOTOGRAPHE ---

@app.route('/photographes')
def photographes():
    conn = get_db_connection()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('photographe_list.html', photographes=photographes)

@app.route('/photographes/add', methods=('GET', 'POST'))
def add_photographe():
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']
        wilaya = request.form['wilaya']
        domaine = request.form['domaine']
        bio = request.form['bio']

        # Get user_id from session if available
        user_id = session.get('user_id')

        conn = get_db_connection()
        conn.execute('INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
                     (user_id, nom, prenom, email, phone, wilaya, domaine, bio))
        conn.commit()
        conn.close()

        if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
            return jsonify({'message': 'Photographe créé avec succès'}), 201
        return redirect(url_for('photographes'))
    return render_template('photographe_form.html', photographe=None)

@app.route('/photographes/edit/<int:id>', methods=('GET', 'POST'))
def edit_photographe(id):
    conn = get_db_connection()
    photographe = conn.execute('SELECT * FROM photographe WHERE id = ?', (id,)).fetchone()
    if photographe is None:
        conn.close()
        return "Photographe non trouvé", 404
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        phone = request.form['phone']
        wilaya = request.form['wilaya']
        domaine = request.form['domaine']
        bio = request.form['bio']

        conn.execute('''
            UPDATE photographe SET nom=?, prenom=?, email=?, phone=?, wilaya=?, domaine=?, bio=?
            WHERE id=?
        ''', (nom, prenom, email, phone, wilaya, domaine, bio, id))
        conn.commit()
        conn.close()
        return redirect(url_for('photographes'))
    conn.close()
    return render_template('photographe_form.html', photographe=photographe)

@app.route('/photographes/delete/<int:id>', methods=('POST',))
def delete_photographe(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM photographe WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('photographes'))

# --- ROUTES PROJET ---

@app.route('/projets')
def projets():
    conn = get_db_connection()
    projets = conn.execute('SELECT * FROM projet').fetchall()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('projet_list.html', projets=projets, clients=clients, photographes=photographes)

@app.route('/projets/add', methods=('GET', 'POST'))
def add_projet():
    conn = get_db_connection()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()

    if request.method == 'POST':
        id_client = request.form['id_client']
        id_photographe = request.form['id_photographe']
        description = request.form['description']
        date_posted = request.form['date_posted']
        image = request.form['image']

        conn = get_db_connection()
        conn.execute('INSERT INTO projet (id_client, id_photographe, description, date_posted, image) VALUES (?, ?, ?, ?, ?)',
                     (id_client, id_photographe, description, date_posted, image))
        conn.commit()
        conn.close()
        return redirect(url_for('projets'))
    return render_template('projet_form.html', projet=None, clients=clients, photographes=photographes)

@app.route('/projets/edit/<int:id>', methods=('GET', 'POST'))
def edit_projet(id):
    conn = get_db_connection()
    projet = conn.execute('SELECT * FROM projet WHERE id = ?', (id,)).fetchone()
    clients = conn.execute('SELECT * FROM client').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    if projet is None:
        conn.close()
        return "Projet non trouvé", 404
    if request.method == 'POST':
        id_client = request.form['id_client']
        id_photographe = request.form['id_photographe']
        description = request.form['description']
        date_posted = request.form['date_posted']
        image = request.form['image']

        conn.execute('''
            UPDATE projet SET id_client=?, id_photographe=?, description=?, date_posted=?, image=?
            WHERE id=?
        ''', (id_client, id_photographe, description, date_posted, image, id))
        conn.commit()
        conn.close()
        return redirect(url_for('projets'))
    conn.close()
    return render_template('projet_form.html', projet=projet, clients=clients, photographes=photographes)

@app.route('/projets/delete/<int:id>', methods=('POST',))
def delete_projet(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM projet WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('projets'))

# --- ROUTES PHOTO ---

@app.route('/photos')
def photos():
    conn = get_db_connection()
    photos = conn.execute('SELECT * FROM photo').fetchall()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    return render_template('photo_list.html', photos=photos, photographes=photographes)

@app.route('/photos/add', methods=('GET', 'POST'))
def add_photo():
    conn = get_db_connection()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    conn.close()
    if request.method == 'POST':
        id_photographe = request.form['id_photographe']
        image = request.form['image']
        description = request.form['description']

        conn = get_db_connection()
        conn.execute('INSERT INTO photo (id_photographe, image, description) VALUES (?, ?, ?)',
                     (id_photographe, image, description))
        conn.commit()
        conn.close()
        return redirect(url_for('photos'))
    return render_template('photo_form.html', photo=None, photographes=photographes)

@app.route('/photos/edit/<int:id>', methods=('GET', 'POST'))
def edit_photo(id):
    conn = get_db_connection()
    photo = conn.execute('SELECT * FROM photo WHERE id = ?', (id,)).fetchone()
    photographes = conn.execute('SELECT * FROM photographe').fetchall()
    if photo is None:
        conn.close()
        return "Photo non trouvée", 404
    if request.method == 'POST':
        id_photographe = request.form['id_photographe']
        image = request.form['image']
        description = request.form['description']

        conn.execute('UPDATE photo SET id_photographe=?, image=?, description=? WHERE id=?',
                     (id_photographe, image, description, id))
        conn.commit()
        conn.close()
        return redirect(url_for('photos'))
    conn.close()
    return render_template('photo_form.html', photo=photo, photographes=photographes)

@app.route('/photos/delete/<int:id>', methods=('POST',))
def delete_photo(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM photo WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('photos'))

# --- ROUTES ANNONCE ---

@app.route('/annonces')
def annonces():
    conn = get_db_connection()
    annonces = conn.execute('SELECT * FROM annonce').fetchall()
    conn.close()
    return render_template('annonce_list.html', annonces=annonces)

@app.route('/annonces/add', methods=('GET', 'POST'))
def add_annonce():
    if request.method == 'POST':
        categore = request.form['categore']
        id_utilisateur = request.form['id_utilisateur']
        etet = request.form['etet']
        image = request.form['image']
        titre = request.form['titre']
        description = request.form['description']
        prix = request.form['prix']
        date_posted = request.form['date_posted']

        conn = get_db_connection()
        conn.execute('''
            INSERT INTO annonce (categore, id_utilisateur, etet, image, titre, description, prix, date_posted)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (categore, id_utilisateur, etet, image, titre, description, prix, date_posted))
        conn.commit()
        conn.close()
        return redirect(url_for('annonces'))
    return render_template('annonce_form.html', annonce=None)

@app.route('/annonces/edit/<int:id>', methods=('GET', 'POST'))
def edit_annonce(id):
    conn = get_db_connection()
    annonce = conn.execute('SELECT * FROM annonce WHERE id = ?', (id,)).fetchone()
    if annonce is None:
        conn.close()
        return "Annonce non trouvée", 404
    if request.method == 'POST':
        categore = request.form['categore']
        id_utilisateur = request.form['id_utilisateur']
        etet = request.form['etet']
        image = request.form['image']
        titre = request.form['titre']
        description = request.form['description']
        prix = request.form['prix']
        date_posted = request.form['date_posted']

        conn.execute('''
            UPDATE annonce SET categore=?, id_utilisateur=?, etet=?, image=?, titre=?, description=?, prix=?, date_posted=?
            WHERE id=?
        ''', (categore, id_utilisateur, etet, image, titre, description, prix, date_posted, id))
        conn.commit()
        conn.close()
        return redirect(url_for('annonces'))
    conn.close()
    return render_template('annonce_form.html', annonce=annonce)

@app.route('/annonces/delete/<int:id>', methods=('POST',))
def delete_annonce(id):
    conn = get_db_connection()
    conn.execute('DELETE FROM annonce WHERE id = ?', (id,))
    conn.commit()
    conn.close()
    return redirect(url_for('annonces'))

@app.route('/api/photographers', methods=['GET'])
def api_photographers():
    domaine = request.args.get('domaine', '')
    wilaya = request.args.get('wilaya', '')

    conn = get_db_connection()
    if conn is None:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        # Fixed table name from 'photographes' to 'photographe' and column names
        query = "SELECT nom as name, prenom, domaine, wilaya FROM photographe WHERE 1=1"
        params = []
        if domaine:
            query += " AND domaine = ?"
            params.append(domaine)
        if wilaya:
            query += " AND wilaya = ?"
            params.append(wilaya)

        photographers = conn.execute(query, params).fetchall()
        conn.close()
    except sqlite3.Error as e:
        if conn:
            conn.close()
        return jsonify({'error': f'Database query failed: {e}'}), 500

    # Transformer en liste de dicts
    results = [dict(row) for row in photographers]
    return jsonify(results)


# --- ROUTE ACCUEIL ---

@app.route('/')
def index():
    return redirect(url_for('clients'))

if __name__ == '__main__':
    app.run(debug=True)
