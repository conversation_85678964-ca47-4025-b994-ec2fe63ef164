#!/usr/bin/env python3
"""
Script to populate the database with sample data
"""
import sqlite3
from datetime import datetime, timedelta
import random
from database_utils import DatabaseManager

def populate_clients():
    """Insert sample clients"""
    print("📝 Adding sample clients...")
    
    clients_data = [
        ("<PERSON>", "<PERSON><PERSON>", "ahmed.ben<PERSON>@email.com", "0555123456"),
        ("<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "0666789012"),
        ("<PERSON>", "<PERSON>udiaf", "<EMAIL>", "0777345678"),
        ("<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "0555987654"),
        ("Youce<PERSON>", "<PERSON><PERSON><PERSON>", "<EMAIL>", "0666321098"),
        ("<PERSON><PERSON>", "Ben<PERSON><PERSON>", "<EMAIL>", "0777654321"),
        ("<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "karim.z<PERSON><PERSON><PERSON>@email.com", "0555456789"),
        ("<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<EMAIL>", "0666123789"),
        ("<PERSON>", "Belkacem", "<EMAIL>", "0777987123"),
        ("Leila", "Meziane", "<EMAIL>", "0555789456")
    ]
    
    db = DatabaseManager()
    success_count = 0
    
    for nom, prenom, email, phone in clients_data:
        success = db.execute_update(
            "INSERT INTO client (nom, prenom, email, phone) VALUES (?, ?, ?, ?)",
            (nom, prenom, email, phone)
        )
        if success:
            success_count += 1
            print(f"  ✅ Added client: {prenom} {nom}")
        else:
            print(f"  ❌ Failed to add client: {prenom} {nom}")
    
    print(f"📊 Added {success_count}/{len(clients_data)} clients\n")
    return success_count

def populate_photographers():
    """Insert sample photographers"""
    print("📸 Adding sample photographers...")
    
    photographers_data = [
        ("Brahim", "Saidi", "<EMAIL>", "0555111222", "Alger", "Portrait", "Photographe professionnel spécialisé dans les portraits et les événements familiaux. 10 ans d'expérience."),
        ("Yasmine", "Touati", "<EMAIL>", "0666333444", "Oran", "Mariage", "Experte en photographie de mariage avec un style romantique et élégant. Couvre tout l'Ouest algérien."),
        ("Rachid", "Amrani", "<EMAIL>", "0777555666", "Constantine", "Nature", "Passionné de photographie de nature et de paysages. Spécialiste des photos en extérieur."),
        ("Soraya", "Benhadj", "<EMAIL>", "0555777888", "Annaba", "Mode", "Photographe de mode et beauté. Travaille avec les agences de mannequins et les marques de cosmétiques."),
        ("Farid", "Ouali", "<EMAIL>", "0666999000", "Tlemcen", "Événementiel", "Spécialiste des événements corporatifs et des conférences. Équipement professionnel haut de gamme."),
        ("Meriem", "Lakhal", "<EMAIL>", "0777111333", "Sétif", "Portrait", "Photographe portraitiste avec studio équipé. Spécialisée dans les photos de famille et enfants."),
        ("Djamel", "Bouazza", "<EMAIL>", "0555444777", "Béjaïa", "Sport", "Photographe sportif couvrant les événements sportifs locaux et nationaux."),
        ("Hanane", "Messaoudi", "<EMAIL>", "0666222555", "Batna", "Mariage", "Photographe de mariage traditionnelle et moderne. Style documentaire et artistique."),
        ("Sofiane", "Cherif", "<EMAIL>", "0777888999", "Ouargla", "Nature", "Photographe du désert et des oasis. Spécialiste des paysages sahariens."),
        ("Amina", "Boukhalfa", "<EMAIL>", "0555666888", "Alger", "Mode", "Photographe de mode et lifestyle. Collabore avec les influenceurs et les marques.")
    ]
    
    db = DatabaseManager()
    success_count = 0
    
    for nom, prenom, email, phone, wilaya, domaine, bio in photographers_data:
        success = db.execute_update(
            "INSERT INTO photographe (nom, prenom, email, phone, wilaya, domaine, bio) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (nom, prenom, email, phone, wilaya, domaine, bio)
        )
        if success:
            success_count += 1
            print(f"  ✅ Added photographer: {prenom} {nom} ({domaine} - {wilaya})")
        else:
            print(f"  ❌ Failed to add photographer: {prenom} {nom}")
    
    print(f"📊 Added {success_count}/{len(photographers_data)} photographers\n")
    return success_count

def populate_projects():
    """Insert sample projects"""
    print("🎯 Adding sample projects...")
    
    # Get existing clients and photographers
    db = DatabaseManager()
    clients = db.execute_query("SELECT id FROM client LIMIT 5")
    photographers = db.execute_query("SELECT id FROM photographe LIMIT 5")
    
    if not clients or not photographers:
        print("  ⚠️ Need clients and photographers first!")
        return 0
    
    projects_data = [
        (clients[0]['id'], photographers[0]['id'], "Séance photo de famille pour célébrer l'anniversaire des grands-parents", "2024-01-15", "family_session.jpg"),
        (clients[1]['id'], photographers[1]['id'], "Mariage traditionnel algérien avec cérémonie au henné", "2024-02-20", "wedding_traditional.jpg"),
        (clients[2]['id'], photographers[2]['id'], "Shooting en extérieur dans les montagnes de Kabylie", "2024-03-10", "nature_mountains.jpg"),
        (clients[3]['id'], photographers[3]['id'], "Séance photo mode pour catalogue de vêtements", "2024-04-05", "fashion_catalog.jpg"),
        (clients[4]['id'], photographers[4]['id'], "Couverture photographique d'un événement d'entreprise", "2024-05-12", "corporate_event.jpg"),
        (clients[0]['id'], photographers[2]['id'], "Photos de grossesse en studio et en extérieur", "2024-06-08", "maternity_shoot.jpg"),
        (clients[1]['id'], photographers[0]['id'], "Portraits professionnels pour LinkedIn", "2024-07-15", "professional_portraits.jpg")
    ]
    
    success_count = 0
    
    for id_client, id_photographe, description, date_posted, image in projects_data:
        success = db.execute_update(
            "INSERT INTO projet (id_client, id_photographe, description, date_posted, image) VALUES (?, ?, ?, ?, ?)",
            (id_client, id_photographe, description, date_posted, image)
        )
        if success:
            success_count += 1
            print(f"  ✅ Added project: {description[:50]}...")
        else:
            print(f"  ❌ Failed to add project: {description[:50]}...")
    
    print(f"📊 Added {success_count}/{len(projects_data)} projects\n")
    return success_count

def populate_photos():
    """Insert sample photos"""
    print("🖼️ Adding sample photos...")
    
    # Get existing photographers
    db = DatabaseManager()
    photographers = db.execute_query("SELECT id FROM photographe LIMIT 5")
    
    if not photographers:
        print("  ⚠️ Need photographers first!")
        return 0
    
    photos_data = [
        (photographers[0]['id'], "portrait_001.jpg", "Portrait élégant en studio avec éclairage professionnel"),
        (photographers[0]['id'], "portrait_002.jpg", "Portrait de famille dans un parc au coucher du soleil"),
        (photographers[1]['id'], "wedding_001.jpg", "Cérémonie de mariage dans une salle de réception"),
        (photographers[1]['id'], "wedding_002.jpg", "Échange des alliances sous un arc de fleurs"),
        (photographers[2]['id'], "nature_001.jpg", "Paysage montagneux au lever du soleil"),
        (photographers[2]['id'], "nature_002.jpg", "Cascade dans la forêt de Chréa"),
        (photographers[3]['id'], "fashion_001.jpg", "Shooting mode urbain dans les rues d'Alger"),
        (photographers[3]['id'], "fashion_002.jpg", "Portrait beauté avec maquillage artistique"),
        (photographers[4]['id'], "event_001.jpg", "Conférence d'entreprise avec speakers"),
        (photographers[4]['id'], "event_002.jpg", "Networking lors d'un événement professionnel")
    ]
    
    success_count = 0
    
    for id_photographe, image, description in photos_data:
        success = db.execute_update(
            "INSERT INTO photo (id_photographe, image, description) VALUES (?, ?, ?)",
            (id_photographe, image, description)
        )
        if success:
            success_count += 1
            print(f"  ✅ Added photo: {description[:40]}...")
        else:
            print(f"  ❌ Failed to add photo: {description[:40]}...")
    
    print(f"📊 Added {success_count}/{len(photos_data)} photos\n")
    return success_count

def populate_announcements():
    """Insert sample announcements"""
    print("📢 Adding sample announcements...")
    
    announcements_data = [
        ("Service", 1, "Disponible", "promo_mariage.jpg", "Promotion Mariage 2024", "Offre spéciale pour les mariages : -20% sur les packages complets. Réservez maintenant!", 15000.0, "2024-01-01"),
        ("Matériel", 2, "Disponible", "camera_rent.jpg", "Location d'équipement photo", "Louez du matériel photo professionnel : appareils, objectifs, éclairage. Tarifs compétitifs.", 500.0, "2024-01-15"),
        ("Service", 3, "Disponible", "portrait_studio.jpg", "Séances portrait en studio", "Studio équipé pour portraits professionnels. Éclairage LED, fonds variés. Sur rendez-vous.", 8000.0, "2024-02-01"),
        ("Formation", 4, "Disponible", "photo_course.jpg", "Cours de photographie", "Apprenez les bases de la photographie : composition, éclairage, post-traitement. Groupes de 6 max.", 12000.0, "2024-02-15"),
        ("Service", 5, "Disponible", "event_photo.jpg", "Couverture événements", "Photographe disponible pour vos événements : conférences, séminaires, inaugurations.", 20000.0, "2024-03-01"),
        ("Matériel", 1, "Disponible", "drone_service.jpg", "Photographie aérienne par drone", "Prises de vue aériennes pour immobilier, événements, paysages. Pilote certifié.", 25000.0, "2024-03-15"),
        ("Service", 2, "Réservé", "baby_photo.jpg", "Séances photo nouveau-né", "Spécialiste des photos de bébés et nouveau-nés. Studio chauffé, accessoires fournis.", 10000.0, "2024-04-01"),
        ("Formation", 3, "Disponible", "lightroom_course.jpg", "Formation Lightroom", "Maîtrisez Adobe Lightroom pour le développement et la retouche de vos photos.", 8000.0, "2024-04-15")
    ]
    
    db = DatabaseManager()
    success_count = 0
    
    for categore, id_utilisateur, etet, image, titre, description, prix, date_posted in announcements_data:
        success = db.execute_update(
            "INSERT INTO annonce (categore, id_utilisateur, etet, image, titre, description, prix, date_posted) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (categore, id_utilisateur, etet, image, titre, description, prix, date_posted)
        )
        if success:
            success_count += 1
            print(f"  ✅ Added announcement: {titre}")
        else:
            print(f"  ❌ Failed to add announcement: {titre}")
    
    print(f"📊 Added {success_count}/{len(announcements_data)} announcements\n")
    return success_count

def main():
    """Main function to populate all tables"""
    print("🚀 Starting database population...")
    print("=" * 60)
    
    total_added = 0
    
    # Populate in order (respecting foreign key constraints)
    total_added += populate_clients()
    total_added += populate_photographers()
    total_added += populate_projects()
    total_added += populate_photos()
    total_added += populate_announcements()
    
    print("=" * 60)
    print(f"🎉 Database population completed!")
    print(f"📊 Total records added: {total_added}")
    
    # Show final statistics
    print("\n📈 Final Database Statistics:")
    from database_utils import get_database_stats
    stats = get_database_stats()
    for table, count in stats.items():
        print(f"  {table.capitalize()}: {count} records")

if __name__ == "__main__":
    main()
