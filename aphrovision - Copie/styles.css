
body {
    font-family: Arial, sans-serif;
    text-align: center;
    background-color: #f8f8f8;
}
header {
    margin-top: 50px;
}
.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 10px;
    background-color: #1a1a1a;
    color: white;
    text-decoration: none;
    border-radius: 5px;
}
/* Styles généraux */
body {
    font-family: Arial, sans-serif;
    background: #121212;
    color: white;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Conteneur principal */
.container {
    display: flex;

    max-width: 900px;
    background-color: #1e1e1e;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 15px rgba(255, 255, 255, 0.2);
}

/* Box du formulaire */
.form-box{
    width: 80%;
    padding: 40px;
    display:flex;
    flex-direction: column;
    justify-content:center;
}
h2 {
    text-align: center;
    margin-bottom: 20px;
    font-size:24px
     
}

.title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: white;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
}


form {
    display: flex;
    flex-direction: column;
}

label {
    margin-top: 10px;
    font-weight: 600;
}

input {
    background: #333;
    color: white;
    border: none;
    padding: 10px;
    margin-top: 5px;
    border-radius: 5px;
}

small {
    font-size: 12px;
    color: #bbb;
}

/* Bouton */
button {
    margin-top: 20px;
    background-color:#ff0000;
    color: white;
    padding: 12px;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    font-weight:bold;
}

button:hover {
    background-color: #cc0000;
}






p {
  position: absolute;
  left: 68%;
  transform: translateX(-50%);
  bottom: -10px;
    

}

p a {
    color:#ffcc00;
    text-decoration: none;
}


/* Styles généraux */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
    text-align: center;
}

/* Header */
header {
    background-color: white;
    padding: 15px;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

nav a {
    margin: 0 10px;
    text-decoration: none;
    color: black;
    font-weight: bold;
}

/* Section des photographes */
.search-filters {
    margin: 20px auto;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.photographers-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin: 20px;
}

.photographer {
    text-align: center;
}

.photographer img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

/* Éditeur d'image */
.editor-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.image-editor {
    width: 300px;
    height: 300px;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 2px dashed #ccc;
}

.controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button {
    padding: 10px;
    background-color: black;
    color: white;
    border: none;
    cursor: pointer;
}
Vous avez envoyé
/* Reset & Général */
{

    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
}

/* Barre de navigation */
header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 30px;
    background-color: white;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.logo {
    font-size: 24px;
    font-weight: bold;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin: 0 15px;
}

nav ul li a {
    text-decoration: none;
    color: black;
    font-weight: bold;
    padding: 10px;
}

nav ul li a.active {
    color: red;
}

/* Section de recherche */
.search-container {
    display: flex;
    justify-content: flex-start;
    padding: 20px 30px;
}

.filters {
    display: flex;
    align-items: center;
    gap: 15px;
}

.filters select, .filters button {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.filters button {
    background-color: black;
    color: white;
    cursor: pointer;
}

/* Section des photographes */
/* Reset & Général */
*{

    margin: 0;
    padding: 0;
    box-sizing: border-box;

}


/* 📌 Barre de navigation */
header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 50px;
    background-color: white;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.logo {
    font-size: 28px;
    font-weight: bold;
    color:greenyellow ;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 25px;
}

nav ul li a {
    text-decoration: none;
    color: black;
    font-weight: bold;
    font-size: 16px;
    padding: 10px;
}

nav ul li a.active {
    color: red;
}

/* 📌 Section de recherche */
.search-container {
    display: flex;
    justify-content: flex-start;
    padding: 20px 50px;
}

.filters {
    display: flex;
    align-items: center;
    gap: 20px;
}

.filters label {
    font-weight: bold;
}

.filters select, .filters button {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.filters button {
    background-color: black;
    color: white;
    cursor: pointer;
    border: none;
    padding: 10px 15px;
}

/* 📌 Section des photographes */
main {
    text-align: center;
    margin-top: 30px;
}

.page-title {
    font-size: 22px;
}

.subtitle {
    color: gray;
    font-style: italic;
    margin-bottom: 20px;
}

/* 📌 Liste des photographes */
.photographers-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 30px;
    padding: 40px;
    max-width: 1000px;
    margin: auto;
}

.photographer {
    text-align: center;
}

.photographer img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease-in-out;
}

.photographer img:hover {
    transform: scale(1.1);
}

.photographer p {
    margin-top: 10px;
    font-size: 16px;
    font-weight: bold;
}


/* 📌 Barre de navigation bien centrée en haut */
header {
    display: flex;
    align-items: center;
    justify-content: space-between; /* Pour séparer le logo, le menu et l'icône */
    padding: 15px 50px;
    background-color: white;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    position: fixed; /* Fixe la barre en haut */
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
}

/* Logo aligné à gauche */
.logo {
    font-size: 24px;
    font-weight: bold;
}

/* Navigation centrée */
nav {
    flex: 1;
    text-align: center;
}

nav ul {
    display: inline-flex; /* Les liens sont bien alignés horizontalement */
    list-style: none;
    gap: 30px;
}

nav ul li a {
    text-decoration: none;
    color: black;
    font-weight: bold;
    font-size: 16px;
    padding: 10px;
}

nav ul li a.active {
    color: red;
}

/* Icône de profil alignée à droite */
.profile-icon img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
}




/* Barre de navigation */

.logo {
    font-size: 22px;
    font-weight: bold;
    color: #333;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 20px;
}

nav ul li a {
    text-decoration: none;
    color: black;
    font-size: 14px;
    font-weight: bold;
}

nav ul li a.active {
    color: #c1121f;
}

.profile-icon img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

/* Section de recherche */
.search-bar {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
}

.search-options {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.search-options span {
    cursor: pointer;
    padding: 5px 15px;
}

.search-options span.active {
    color: #c1121f;
    border-bottom: 2px solid #c1121f;
}

.search-box {
    margin-top: 10px;
}

.search-box input {
    width: 60%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.search-box button {
    padding: 8px 15px;
    background-color: #c1121f;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* Galerie */
.gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 15px;
    padding: 30px;
    max-width: 1100px;
    margin: auto;
}

.photo-card {
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.photo-card:hover {
    transform: scale(1.05);
}

.photo-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}
