<!DOCTYPE html>
<html lang="fr">
<head>
<meta charset="UTF-8" />
<title>Détails de l'article - AphrovisionShop</title>
<style>
  /* Reset simple */
  *, *::before, *::after {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #dddddd;
    color: #333;
  }

  /* Navbar fixe */
  header {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #ffffff;
    color: rgb(0, 0, 0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
  }

  .top-nav {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 30px;
  }

  .top-nav h1 {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    margin-right: 40px;
    color: #000000;
    letter-spacing: 2px;
    cursor: default;
  }

  .top-nav a {
    text-decoration: none;
    margin-right: 25px;
    font-weight: 600;
    color: #000000;
    font-size: 15px;
    transition: color 0.3s ease;
  }
  .top-nav a:hover {
    color: #343333;
  }

  /* Container principal */
  .container {
    max-width: 1200px;
    margin: 120px auto 60px; /* marge top plus grande pour navbar fixe */
    background-color: white;
    box-shadow: 0 8px 20px rgba(0,0,0,0.05);
    display: flex;
    gap: 50px;
    padding: 40px 40px 50px;
    border-radius: 12px;
  }

  /* Responsive */
  @media (max-width: 900px) {
    .container {
      flex-direction: column;
      margin: 140px 20px 60px;
      padding: 30px 20px;
    }
    .details-section {
      padding-left: 0;
    }
  }

  /* Image section */
  .image-section {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-section img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    border: 1px solid #eee;
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
  }

  /* Détails produit */
  .details-section {
    flex: 2;
    padding-left: 50px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .details-section h2 {
    font-size: 26px;
    margin-bottom: 15px;
    color: #222;
  }

  .etat {
    font-weight: 700;
    color: #28a745; /* vert */
    margin-bottom: 25px;
    font-size: 16px;
  }

  .description {
    font-size: 16px;
    line-height: 1.7;
    color: #555;
    margin-bottom: 35px;
  }

  .price {
    font-size: 28px;
    font-weight: 800;
    color: #bb0000;
    margin-bottom: 35px;
  }

  /* Boutons d'action */
  .actions {
    display: flex;
    gap: 20px;
  }

  .btn {
    padding: 14px 30px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 700;
    font-size: 16px;
    transition: background-color 0.3s ease;
  }

  .btn-acheter {
    background-color: #000000;
    color: white;
  }

  .btn-acheter:hover {
    background-color: #000000;
  }

  .btn-ajouter {
    background-color: #000000;
    color: white;
  }

  .btn-ajouter:hover {
    background-color: #333;
  }

  /* Footer */
  footer {
    text-align: center;
    padding: 25px 10px;
    font-size: 14px;
    color: #999;
  }
</style>
</head>
<body>

<header>
  <div class="top-nav">
    <h1>Aphrovision</h1>
   
    <a href="explore.html">EXPLORER</a>
    <a href="profile_view.html">PROFIL</a>
    <a href="photographers.html">PHOTOGRAPHES</a>
    <a href="poster.html">POSTER</a>
    <a href="#">MESSAGERIE</a>
    <a href="shop.html">MAGASIN</a>
    
  </div>
</header>

<div class="container">
  <div class="image-section">
    <img src="photo10.png" alt="Fujifilm XF 56mm f/1.2 R" />
  </div>
  <div class="details-section">
    <h2>Objectif hybride Fujifilm XF 56 mm f/1,2 R</h2>
    <div class="etat">État : bon état</div>
    <div class="description">
      Une ouverture prodigieuse à F1.2 !<br />
      1 lentille asphérique et 2 lentilles en verre ED.<br />
      Une finition haut de gamme en métal.<br />
      Effets de Bokeh magnifiques.<br />
      Dédié aux hybrides FUJIFILM à monture X.
    </div>
    <div class="price">188 790,00 DA</div>
    <div class="actions">
      <button class="btn btn-acheter">Acheter maintenant</button>
      <button class="btn btn-ajouter">Ajouter au panier</button>
    </div>
  </div>
</div>

<footer>
  &copy; 2025 AphrovisionShop. Tous droits réservés.
</footer>

<script>
  // Exemple de fonction pour "Ajouter au panier"
  document.querySelector('.btn-ajouter').addEventListener('click', function () {
    alert("Produit ajouté au panier !");
    // Ici tu pourrais aussi stocker le produit dans le localStorage ou l'envoyer au backend
  });

  // Exemple de fonction pour "Acheter maintenant"
  document.querySelector('.btn-acheter').addEventListener('click', function () {
    alert("Redirection vers la page de paiement...");
    // Tu peux ici rediriger vers une page de paiement ou ouvrir une modal
    window.location.href = 'checkout.html'; // Remplace par la vraie page de paiement
  });
  

</script>


</body>
</html>
