{% extends "base.html" %}

{% block title %}
{% if client %}Modifier Client{% else %}Ajouter Client{% endif %} - AphroVision
{% endblock %}

{% block content %}
<div class="card">
    <h1>
        {% if client %}
            ✏️ Modifier le Client: {{ client.prenom }} {{ client.nom }}
        {% else %}
            ➕ Ajouter un Nouveau Client
        {% endif %}
    </h1>

    <form method="POST">
        <div class="form-group">
            <label for="nom">Nom *</label>
            <input type="text" id="nom" name="nom" 
                   value="{{ client.nom if client else '' }}" 
                   required placeholder="Ex: Benali">
        </div>

        <div class="form-group">
            <label for="prenom">Prénom *</label>
            <input type="text" id="prenom" name="prenom" 
                   value="{{ client.prenom if client else '' }}" 
                   required placeholder="Ex: Ahmed">
        </div>

        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" 
                   value="{{ client.email if client else '' }}" 
                   required placeholder="Ex: <EMAIL>">
        </div>

        <div class="form-group">
            <label for="phone">Téléphone *</label>
            <input type="tel" id="phone" name="phone" 
                   value="{{ client.phone if client else '' }}" 
                   required placeholder="Ex: 0555123456">
        </div>

        <div style="margin-top: 2rem;">
            <button type="submit" class="btn btn-success">
                {% if client %}💾 Mettre à Jour{% else %}➕ Ajouter{% endif %}
            </button>
            <a href="/clients" class="btn">↩️ Retour à la Liste</a>
        </div>
    </form>
</div>

{% if client %}
<div class="card">
    <h2>📊 Informations du Client</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        <div>
            <strong>ID:</strong> {{ client.id }}
        </div>
        <div>
            <strong>Nom Complet:</strong> {{ client.prenom }} {{ client.nom }}
        </div>
        <div>
            <strong>Email:</strong> {{ client.email }}
        </div>
        <div>
            <strong>Téléphone:</strong> {{ client.phone }}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
