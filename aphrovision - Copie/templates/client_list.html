{% extends "base.html" %}

{% block title %}Clients - AphroVision{% endblock %}

{% block content %}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
        <h1>👥 Gestion des Clients</h1>
        <a href="/clients/add" class="btn btn-success">➕ Ajouter un Client</a>
    </div>

    {% if clients %}
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ clients|length }}</div>
                <div class="stat-label">Clients Total</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nom Complet</th>
                    <th>Email</th>
                    <th>Téléphone</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for client in clients %}
                <tr>
                    <td>{{ client.id }}</td>
                    <td>{{ client.prenom }} {{ client.nom }}</td>
                    <td>📧 {{ client.email }}</td>
                    <td>📱 {{ client.phone }}</td>
                    <td>
                        <a href="/clients/edit/{{ client.id }}" class="btn" style="padding: 0.5rem 1rem;">✏️ Modifier</a>
                        <form method="POST" action="/clients/delete/{{ client.id }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem;" 
                                    onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce client ?')">
                                🗑️ Supprimer
                            </button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="alert alert-info" style="text-align: center; padding: 3rem;">
            <h3>Aucun client trouvé</h3>
            <p>Commencez par ajouter votre premier client.</p>
            <a href="/clients/add" class="btn btn-success">➕ Ajouter le Premier Client</a>
        </div>
    {% endif %}
</div>

<div class="card">
    <h2>📊 Actions Rapides</h2>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <a href="/clients/add" class="btn">➕ Nouveau Client</a>
        <a href="/photographes" class="btn">📸 Voir Photographes</a>
        <a href="/projets" class="btn">🎯 Voir Projets</a>
        <a href="/api/photographers" class="btn" target="_blank">🔗 API Photographes</a>
    </div>
</div>
{% endblock %}
