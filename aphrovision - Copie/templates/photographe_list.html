{% extends "base.html" %}

{% block title %}Photographes - AphroVision{% endblock %}

{% block content %}
<div class="card">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
        <h1>📸 Gestion des Photographes</h1>
        <a href="/photographes/add" class="btn btn-success">➕ Ajouter un Photographe</a>
    </div>

    {% if photographes %}
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">{{ photographes|length }}</div>
                <div class="stat-label">Photographes Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ photographes|selectattr("domaine", "equalto", "Portrait")|list|length }}</div>
                <div class="stat-label">Spécialistes Portrait</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ photographes|selectattr("domaine", "equalto", "Mariage")|list|length }}</div>
                <div class="stat-label">Spécialistes Mariage</div>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Photographe</th>
                    <th>Spécialité</th>
                    <th>Wilaya</th>
                    <th>Contact</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for photographe in photographes %}
                <tr>
                    <td>{{ photographe.id }}</td>
                    <td>
                        <strong>{{ photographe.prenom }} {{ photographe.nom }}</strong>
                        <br>
                        <small style="color: #666;">{{ photographe.bio[:50] }}...</small>
                    </td>
                    <td>
                        <span style="background: #667eea; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.9rem;">
                            {{ photographe.domaine }}
                        </span>
                    </td>
                    <td>📍 {{ photographe.wilaya }}</td>
                    <td>
                        📧 {{ photographe.email }}<br>
                        📱 {{ photographe.phone }}
                    </td>
                    <td>
                        <a href="/photographes/edit/{{ photographe.id }}" class="btn" style="padding: 0.5rem 1rem;">✏️ Modifier</a>
                        <form method="POST" action="/photographes/delete/{{ photographe.id }}" style="display: inline;">
                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem 1rem;" 
                                    onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce photographe ?')">
                                🗑️ Supprimer
                            </button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="alert alert-info" style="text-align: center; padding: 3rem;">
            <h3>Aucun photographe trouvé</h3>
            <p>Commencez par ajouter votre premier photographe.</p>
            <a href="/photographes/add" class="btn btn-success">➕ Ajouter le Premier Photographe</a>
        </div>
    {% endif %}
</div>

<div class="card">
    <h2>🔍 Recherche par API</h2>
    <p>Testez l'API de recherche des photographes :</p>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        <a href="/api/photographers" class="btn" target="_blank">🔗 Tous les Photographes</a>
        <a href="/api/photographers?domaine=Portrait" class="btn" target="_blank">👤 Spécialistes Portrait</a>
        <a href="/api/photographers?domaine=Mariage" class="btn" target="_blank">💒 Spécialistes Mariage</a>
        <a href="/api/photographers?wilaya=Alger" class="btn" target="_blank">📍 Photographes d'Alger</a>
    </div>
</div>
{% endblock %}
