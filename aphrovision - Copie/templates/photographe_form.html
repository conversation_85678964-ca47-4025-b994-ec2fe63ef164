{% extends "base.html" %}

{% block title %}
{% if photographe %}Modifier Photographe{% else %}Ajouter Photographe{% endif %} - AphroVision
{% endblock %}

{% block content %}
<div class="card">
    <h1>
        {% if photographe %}
            ✏️ Modifier le Photographe: {{ photographe.prenom }} {{ photographe.nom }}
        {% else %}
            ➕ Ajouter un Nouveau Photographe
        {% endif %}
    </h1>

    <form method="POST">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="nom">Nom *</label>
                <input type="text" id="nom" name="nom" 
                       value="{{ photographe.nom if photographe else '' }}" 
                       required placeholder="Ex: Saidi">
            </div>

            <div class="form-group">
                <label for="prenom">Prénom *</label>
                <input type="text" id="prenom" name="prenom" 
                       value="{{ photographe.prenom if photographe else '' }}" 
                       required placeholder="Ex: Brahim">
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" 
                       value="{{ photographe.email if photographe else '' }}" 
                       required placeholder="Ex: <EMAIL>">
            </div>

            <div class="form-group">
                <label for="phone">Téléphone *</label>
                <input type="tel" id="phone" name="phone" 
                       value="{{ photographe.phone if photographe else '' }}" 
                       required placeholder="Ex: 0555111222">
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div class="form-group">
                <label for="wilaya">Wilaya *</label>
                <select id="wilaya" name="wilaya" required>
                    <option value="">Choisir une wilaya</option>
                    <option value="Alger" {{ 'selected' if photographe and photographe.wilaya == 'Alger' else '' }}>Alger</option>
                    <option value="Oran" {{ 'selected' if photographe and photographe.wilaya == 'Oran' else '' }}>Oran</option>
                    <option value="Constantine" {{ 'selected' if photographe and photographe.wilaya == 'Constantine' else '' }}>Constantine</option>
                    <option value="Annaba" {{ 'selected' if photographe and photographe.wilaya == 'Annaba' else '' }}>Annaba</option>
                    <option value="Tlemcen" {{ 'selected' if photographe and photographe.wilaya == 'Tlemcen' else '' }}>Tlemcen</option>
                    <option value="Sétif" {{ 'selected' if photographe and photographe.wilaya == 'Sétif' else '' }}>Sétif</option>
                    <option value="Béjaïa" {{ 'selected' if photographe and photographe.wilaya == 'Béjaïa' else '' }}>Béjaïa</option>
                    <option value="Batna" {{ 'selected' if photographe and photographe.wilaya == 'Batna' else '' }}>Batna</option>
                    <option value="Ouargla" {{ 'selected' if photographe and photographe.wilaya == 'Ouargla' else '' }}>Ouargla</option>
                </select>
            </div>

            <div class="form-group">
                <label for="domaine">Domaine de Spécialité *</label>
                <select id="domaine" name="domaine" required>
                    <option value="">Choisir un domaine</option>
                    <option value="Portrait" {{ 'selected' if photographe and photographe.domaine == 'Portrait' else '' }}>Portrait</option>
                    <option value="Mariage" {{ 'selected' if photographe and photographe.domaine == 'Mariage' else '' }}>Mariage</option>
                    <option value="Nature" {{ 'selected' if photographe and photographe.domaine == 'Nature' else '' }}>Nature</option>
                    <option value="Mode" {{ 'selected' if photographe and photographe.domaine == 'Mode' else '' }}>Mode</option>
                    <option value="Événementiel" {{ 'selected' if photographe and photographe.domaine == 'Événementiel' else '' }}>Événementiel</option>
                    <option value="Sport" {{ 'selected' if photographe and photographe.domaine == 'Sport' else '' }}>Sport</option>
                </select>
            </div>
        </div>

        <div class="form-group">
            <label for="bio">Biographie / Description *</label>
            <textarea id="bio" name="bio" rows="4" required 
                      placeholder="Décrivez votre expérience, votre style et vos spécialités...">{{ photographe.bio if photographe else '' }}</textarea>
        </div>

        <div style="margin-top: 2rem;">
            <button type="submit" class="btn btn-success">
                {% if photographe %}💾 Mettre à Jour{% else %}➕ Ajouter{% endif %}
            </button>
            <a href="/photographes" class="btn">↩️ Retour à la Liste</a>
        </div>
    </form>
</div>

{% if photographe %}
<div class="card">
    <h2>📊 Profil du Photographe</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
        <div>
            <strong>ID:</strong> {{ photographe.id }}
        </div>
        <div>
            <strong>Nom Complet:</strong> {{ photographe.prenom }} {{ photographe.nom }}
        </div>
        <div>
            <strong>Spécialité:</strong> 
            <span style="background: #667eea; color: white; padding: 0.2rem 0.6rem; border-radius: 10px;">
                {{ photographe.domaine }}
            </span>
        </div>
        <div>
            <strong>Localisation:</strong> 📍 {{ photographe.wilaya }}
        </div>
    </div>
    <div style="margin-top: 1rem;">
        <strong>Biographie:</strong>
        <p style="margin-top: 0.5rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
            {{ photographe.bio }}
        </p>
    </div>
</div>
{% endif %}
{% endblock %}
