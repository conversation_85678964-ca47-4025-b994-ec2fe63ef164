<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Photographes - Aphrovision</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f2f2f2;
      color: #333;
    }

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 2em;
      color: #000000;
    }

    nav ul {
      list-style: none;
      display: flex;
      gap: 25px;
    }

    nav a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
    }

    nav a:hover {
      color: #f73535;
    }

    .filters-container {
      max-width: 1200px;
      margin: 40px auto;
      background-color: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    }

    .section-title {
      text-align: center;
      font-size: 2em;
      margin-bottom: 30px;
      font-weight: bold;
    }

    .content {
      display: flex;
      gap: 40px;
      flex-wrap: wrap;
    }

    .filters {
      flex: 1;
      min-width: 200px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .filters label {
      font-size: 1em;
    }

    .filters select,
    .filters button {
      padding: 10px;
      font-size: 1em;
      border: 1px solid #ccc;
      border-radius: 6px;
      background-color: #fff;
      color: #333;
      transition: 0.3s;
    }

    .filters button:hover {
      background-color: #629208;
      color: white;
      border-color: #ff4d4d;
    }

    .photographers-list {
      flex: 3;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 20px;
    }

    .photographer {
      background-color: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
      text-align: center;
      padding: 15px;
      transition: transform 0.3s ease;
    }

    .photographer:hover {
      transform: scale(1.03);
    }

    .photographer-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-bottom: 15px;
      border: 2px solid #007bff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      margin: 0 auto 15px auto;
      box-shadow: 0 3px 10px rgba(0, 123, 255, 0.3);
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      cursor: pointer;
    }

    .photographer-icon:hover {
      transform: scale(1.1);
      box-shadow: 0 5px 15px rgba(0, 123, 255, 0.5);
    }

    .photographer p {
      font-weight: bold;
    }

    /* Popup Modal Styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background-color: white;
      margin: 5% auto;
      padding: 0;
      border-radius: 15px;
      width: 90%;
      max-width: 500px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      animation: modalSlideIn 0.3s ease-out;
      overflow: hidden;
    }

    @keyframes modalSlideIn {
      from {
        transform: translateY(-50px);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .modal-header {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      padding: 20px;
      text-align: center;
      position: relative;
    }

    .modal-header h2 {
      margin: 0;
      font-size: 1.5em;
    }

    .close {
      position: absolute;
      right: 15px;
      top: 15px;
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s;
    }

    .close:hover {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .modal-body {
      padding: 25px;
    }

    .modal-profile-icon {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 3rem;
      margin: 0 auto 20px auto;
      box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
    }

    .modal-info {
      text-align: center;
    }

    .modal-info h3 {
      margin: 0 0 10px 0;
      color: #333;
      font-size: 1.3em;
    }

    .modal-info .specialty {
      background: #007bff;
      color: white;
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 0.9em;
      display: inline-block;
      margin-bottom: 15px;
    }

    .modal-details {
      text-align: left;
      margin-top: 20px;
    }

    .modal-details .detail-item {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .modal-details .detail-icon {
      width: 20px;
      text-align: center;
      color: #007bff;
    }

    .modal-bio {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin-top: 15px;
      border-left: 4px solid #007bff;
    }

    .modal-bio h4 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 1em;
    }

    .modal-bio p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }

    @media (max-width: 768px) {
      .content {
        flex-direction: column;
      }

      .modal-content {
        width: 95%;
        margin: 10% auto;
      }
    }
  </style>
</head>
<body>

  <header>
    <div class="logo">Aphrovision</div>
    <nav>
      <ul>
        <li><a href="explore.html">Explorer</a></li>
        <li><a href="profile_view.html">Profil</a></li>
        <li><a href="photographers.html">Photographes</a></li>
        <li><a href="poster.html">Poster</a></li>
        <li><a href="#">Messagerie</a></li>
        <li><a href="shop.html">Magasin</a></li>
      </ul>
    </nav>
    <div class="notif-icon">🔔</div>
  </header>

  <main>
    <section class="filters-container">
      <div class="section-title">Trouvez nos photographes</div>
      <div class="content">
        <div class="filters">
          <label for="domaines">Domaines :</label>
          <select id="domaines">
            <option value="">Tous les domaines</option>
            <option value="Portrait">Portrait</option>
            <option value="Mariage">Mariage</option>
            <option value="Mode">Mode</option>
            <option value="Nature">Nature</option>
            <option value="Événementiel">Événementiel</option>
            <option value="Sport">Sport</option>
          </select>

          <label for="wilaya">Wilaya :</label>
          <select id="wilaya">
  <option value="">Toutes les wilayas</option>
  <option value="Tizi-Ouzou">Tizi-Ouzou</option>
  <option value="Béjaïa">Béjaïa</option>
  <option value="Laghouat">Laghouat</option>
  <option value="Oum El Bouaghi">Oum El Bouaghi</option>
  <option value="Batna">Batna</option>
  <option value="Chlef">Chlef</option>
  <option value="Biskra">Biskra</option>
  <option value="Béchar">Béchar</option>
  <option value="Blida">Blida</option>
  <option value="Bouira">Bouira</option>
  <option value="Tamanrasset">Tamanrasset</option>
  <option value="Tébessa">Tébessa</option>
  <option value="Tlemcen">Tlemcen</option>
  <option value="Tiaret">Tiaret</option>
  <option value="Adrar">Adrar</option>
  <option value="Alger">Alger</option>
  <option value="Djelfa">Djelfa</option>
  <option value="Jijel">Jijel</option>
  <option value="Sétif">Sétif</option>
  <option value="Saïda">Saïda</option>
  <option value="Skikda">Skikda</option>
  <option value="Sidi Bel Abbès">Sidi Bel Abbès</option>
  <option value="Annaba">Annaba</option>
  <option value="Guelma">Guelma</option>
  <option value="Constantine">Constantine</option>
  <option value="Médéa">Médéa</option>
  <option value="Mostaganem">Mostaganem</option>
  <option value="MSila">M'Sila</option>
  <option value="Mascara">Mascara</option>
  <option value="Ouargla">Ouargla</option>
  <option value="Oran">Oran</option>
  <option value="El Bayadh">El Bayadh</option>
  <option value="Illizi">Illizi</option>
  <option value="Bordj Bou Arréridj">Bordj Bou Arréridj</option>
  <option value="Boumerdès">Boumerdès</option>
  <option value="El Tarf">El Tarf</option>
  <option value="Tindouf">Tindouf</option>
  <option value="Tissemsilt">Tissemsilt</option>
  <option value="El Oued">El Oued</option>
  <option value="Khenchela">Khenchela</option>
  <option value="Souk Ahras">Souk Ahras</option>
  <option value="Tipaza">Tipaza</option>
  <option value="Mila">Mila</option>
  <option value="Aïn Defla">Aïn Defla</option>
  <option value="Naâma">Naâma</option>
  <option value="Aïn Témouchent">Aïn Témouchent</option>
  <option value="Ghardaïa">Ghardaïa</option>
  <option value="Relizane">Relizane</option>
  <option value="Timimoun">Timimoun</option>
  <option value="Bordj Badji Mokhtar">Bordj Badji Mokhtar</option>
  <option value="Ouled Djellal">Ouled Djellal</option>
  <option value="Béni Abbès">Béni Abbès</option>
  <option value="In Salah">In Salah</option>
  <option value="In Guezzam">In Guezzam</option>
  <option value="Touggourt">Touggourt</option>
  <option value="Djanet">Djanet</option>
  <option value="El M'Ghair">El M'Ghair</option>
  <option value="El Menia">El Menia</option>
</select>

          <button id="filterBtn" type="button">Appliquer</button>
        </div>

        <div class="photographers-list" id="photographersList">
          <div style="text-align: center; padding: 20px;">
            <p>Chargement des photographes...</p>
          </div>
        </div>
      </div>
    </section>
  </main>
  <script>
  let allPhotographers = [];

  // Fonction pour afficher la liste
  function displayPhotographers(list) {
    const container = document.getElementById('photographersList');
    container.innerHTML = ''; // vide le contenu

    if (list.length === 0) {
      container.innerHTML = "<div style='text-align: center; padding: 20px;'><p>Aucun photographe trouvé.</p></div>";
      return;
    }

    list.forEach(p => {
      const div = document.createElement("div");
      div.className = "photographer";

      div.innerHTML = `
        <div class="photographer-icon" onclick="showPhotographerDetails(${JSON.stringify(p).replace(/"/g, '&quot;')})">
          👤
        </div>
        <p><strong>${p.nom} ${p.prenom}</strong></p>
        <p style="font-size: 0.9em; color: #666;">${p.domaine}</p>
        <p style="font-size: 0.8em; color: #888;">${p.wilaya}</p>
        <p style="font-size: 0.8em; margin-top: 5px;">${p.bio ? p.bio.substring(0, 50) + '...' : ''}</p>
      `;
      container.appendChild(div);
    });
  }

  // Fonction pour charger les photographes depuis l'API
  async function loadPhotographers(domaine = '', wilaya = '') {
    try {
      const params = new URLSearchParams();
      if (domaine) params.append('domaine', domaine);
      if (wilaya) params.append('wilaya', wilaya);

      const url = `/api/photographers${params.toString() ? '?' + params.toString() : ''}`;
      const response = await fetch(url);

      if (response.ok) {
        const photographers = await response.json();
        allPhotographers = photographers;
        displayPhotographers(photographers);
      } else {
        console.error('Erreur lors du chargement des photographes');
        document.getElementById('photographersList').innerHTML =
          "<div style='text-align: center; padding: 20px;'><p>Erreur lors du chargement des photographes.</p></div>";
      }
    } catch (error) {
      console.error('Erreur:', error);
      document.getElementById('photographersList').innerHTML =
        "<div style='text-align: center; padding: 20px;'><p>Erreur de connexion au serveur.</p></div>";
    }
  }



  // Chargement initial
  loadPhotographers();

  // Événement sur le bouton de filtrage - Multiple event listeners for better compatibility
  document.addEventListener('DOMContentLoaded', function() {
    const filterBtn = document.getElementById('filterBtn');
    const domaineSelect = document.getElementById('domaines');
    const wilayaSelect = document.getElementById('wilaya');

    // Button click event
    if (filterBtn) {
      filterBtn.addEventListener("click", function() {
        console.log('Filter button clicked');
        const domaine = domaineSelect.value;
        const wilaya = wilayaSelect.value;
        console.log('Filtering by:', { domaine, wilaya });
        loadPhotographers(domaine, wilaya);
      });
    }

    // Also add change events to selects for immediate filtering
    if (domaineSelect) {
      domaineSelect.addEventListener("change", function() {
        const domaine = this.value;
        const wilaya = wilayaSelect.value;
        loadPhotographers(domaine, wilaya);
      });
    }

    if (wilayaSelect) {
      wilayaSelect.addEventListener("change", function() {
        const domaine = domaineSelect.value;
        const wilaya = this.value;
        loadPhotographers(domaine, wilaya);
      });
    }
  });

  // Fallback event listener
  document.querySelector(".filters button").addEventListener("click", () => {
    console.log('Fallback filter button clicked');
    const domaine = document.getElementById("domaines").value;
    const wilaya = document.getElementById("wilaya").value;
    loadPhotographers(domaine, wilaya);
  });

  // Modal functionality
  function showPhotographerDetails(photographer) {
    // Populate modal with photographer data
    document.getElementById('modalName').textContent = `${photographer.nom} ${photographer.prenom}`;
    document.getElementById('modalSpecialty').textContent = photographer.domaine;
    document.getElementById('modalLocation').textContent = photographer.wilaya;
    document.getElementById('modalEmail').textContent = photographer.email;
    document.getElementById('modalPhone').textContent = photographer.phone;
    document.getElementById('modalBio').textContent = photographer.bio;

    // Show modal
    document.getElementById('photographerModal').style.display = 'block';
  }

  // Close modal functionality
  document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('photographerModal');
    const closeBtn = document.querySelector('.close');

    // Close modal when clicking X
    closeBtn.addEventListener('click', function() {
      modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
      if (event.target === modal) {
        modal.style.display = 'none';
      }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape' && modal.style.display === 'block') {
        modal.style.display = 'none';
      }
    });
  });
</script>

  <!-- Photographer Details Modal -->
  <div id="photographerModal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Profil Photographe</h2>
        <span class="close">&times;</span>
      </div>
      <div class="modal-body">
        <div class="modal-profile-icon">
          👤
        </div>
        <div class="modal-info">
          <h3 id="modalName"></h3>
          <span id="modalSpecialty" class="specialty"></span>
        </div>
        <div class="modal-details">
          <div class="detail-item">
            <span class="detail-icon">📍</span>
            <span id="modalLocation"></span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">📧</span>
            <span id="modalEmail"></span>
          </div>
          <div class="detail-item">
            <span class="detail-icon">📱</span>
            <span id="modalPhone"></span>
          </div>
        </div>
        <div class="modal-bio">
          <h4>À propos</h4>
          <p id="modalBio"></p>
        </div>
      </div>
    </div>
  </div>

</body>
</html>
