<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Photographes - Aphrovision</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #f2f2f2;
      color: #333;
    }

    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 40px;
      background-color: white;
      border-bottom: 1px solid #ddd;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 2em;
      color: #000000;
    }

    nav ul {
      list-style: none;
      display: flex;
      gap: 25px;
    }

    nav a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.3s;
    }

    nav a:hover {
      color: #f73535;
    }

    .filters-container {
      max-width: 1200px;
      margin: 40px auto;
      background-color: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    }

    .section-title {
      text-align: center;
      font-size: 2em;
      margin-bottom: 30px;
      font-weight: bold;
    }

    .content {
      display: flex;
      gap: 40px;
      flex-wrap: wrap;
    }

    .filters {
      flex: 1;
      min-width: 200px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .filters label {
      font-size: 1em;
    }

    .filters select,
    .filters button {
      padding: 10px;
      font-size: 1em;
      border: 1px solid #ccc;
      border-radius: 6px;
      background-color: #fff;
      color: #333;
      transition: 0.3s;
    }

    .filters button:hover {
      background-color: #629208;
      color: white;
      border-color: #ff4d4d;
    }

    .photographers-list {
      flex: 3;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 20px;
    }

    .photographer {
      background-color: white;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
      text-align: center;
      padding: 15px;
      transition: transform 0.3s ease;
    }

    .photographer:hover {
      transform: scale(1.03);
    }

    .photographer img {
      width: 150px;
      height: 150px;
      object-fit: cover;
      border-radius: 50%;
      margin-bottom: 10px;
      border: 2px solid #000;
    }

    .photographer p {
      font-weight: bold;
    }

    @media (max-width: 768px) {
      .content {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>

  <header>
    <div class="logo">Aphrovision</div>
    <nav>
      <ul>
        <li><a href="explore.html">Explorer</a></li>
        <li><a href="profile_view.html">Profil</a></li>
        <li><a href="photographers.html">Photographes</a></li>
        <li><a href="poster.html">Poster</a></li>
        <li><a href="#">Messagerie</a></li>
        <li><a href="shop.html">Magasin</a></li>
      </ul>
    </nav>
    <div class="notif-icon">🔔</div>
  </header>

  <main>
    <section class="filters-container">
      <div class="section-title">Trouvez nos photographes</div>
      <div class="content">
        <div class="filters">
          <label for="domaines">Domaines :</label>
          <select id="domaines">
            <option value="">Tous les domaines</option>
            <option value="Portrait">Portrait</option>
            <option value="Mariage">Mariage</option>
            <option value="Mode">Mode</option>
            <option value="Nature">Nature</option>
            <option value="Événementiel">Événementiel</option>
            <option value="Sport">Sport</option>
          </select>

          <label for="wilaya">Wilaya :</label>
          <select id="wilaya">
  <option value="">Toutes les wilayas</option>
  <option value="Tizi-Ouzou">Tizi-Ouzou</option>
  <option value="Béjaïa">Béjaïa</option>
  <option value="Laghouat">Laghouat</option>
  <option value="Oum El Bouaghi">Oum El Bouaghi</option>
  <option value="Batna">Batna</option>
  <option value="Chlef">Chlef</option>
  <option value="Biskra">Biskra</option>
  <option value="Béchar">Béchar</option>
  <option value="Blida">Blida</option>
  <option value="Bouira">Bouira</option>
  <option value="Tamanrasset">Tamanrasset</option>
  <option value="Tébessa">Tébessa</option>
  <option value="Tlemcen">Tlemcen</option>
  <option value="Tiaret">Tiaret</option>
  <option value="Adrar">Adrar</option>
  <option value="Alger">Alger</option>
  <option value="Djelfa">Djelfa</option>
  <option value="Jijel">Jijel</option>
  <option value="Sétif">Sétif</option>
  <option value="Saïda">Saïda</option>
  <option value="Skikda">Skikda</option>
  <option value="Sidi Bel Abbès">Sidi Bel Abbès</option>
  <option value="Annaba">Annaba</option>
  <option value="Guelma">Guelma</option>
  <option value="Constantine">Constantine</option>
  <option value="Médéa">Médéa</option>
  <option value="Mostaganem">Mostaganem</option>
  <option value="MSila">M'Sila</option>
  <option value="Mascara">Mascara</option>
  <option value="Ouargla">Ouargla</option>
  <option value="Oran">Oran</option>
  <option value="El Bayadh">El Bayadh</option>
  <option value="Illizi">Illizi</option>
  <option value="Bordj Bou Arréridj">Bordj Bou Arréridj</option>
  <option value="Boumerdès">Boumerdès</option>
  <option value="El Tarf">El Tarf</option>
  <option value="Tindouf">Tindouf</option>
  <option value="Tissemsilt">Tissemsilt</option>
  <option value="El Oued">El Oued</option>
  <option value="Khenchela">Khenchela</option>
  <option value="Souk Ahras">Souk Ahras</option>
  <option value="Tipaza">Tipaza</option>
  <option value="Mila">Mila</option>
  <option value="Aïn Defla">Aïn Defla</option>
  <option value="Naâma">Naâma</option>
  <option value="Aïn Témouchent">Aïn Témouchent</option>
  <option value="Ghardaïa">Ghardaïa</option>
  <option value="Relizane">Relizane</option>
  <option value="Timimoun">Timimoun</option>
  <option value="Bordj Badji Mokhtar">Bordj Badji Mokhtar</option>
  <option value="Ouled Djellal">Ouled Djellal</option>
  <option value="Béni Abbès">Béni Abbès</option>
  <option value="In Salah">In Salah</option>
  <option value="In Guezzam">In Guezzam</option>
  <option value="Touggourt">Touggourt</option>
  <option value="Djanet">Djanet</option>
  <option value="El M'Ghair">El M'Ghair</option>
  <option value="El Menia">El Menia</option>
</select>

          <button id="filterBtn" type="button">Appliquer</button>
        </div>

        <div class="photographers-list" id="photographersList">
          <div style="text-align: center; padding: 20px;">
            <p>Chargement des photographes...</p>
          </div>
        </div>
      </div>
    </section>
  </main>
  <script>
  let allPhotographers = [];

  // Fonction pour afficher la liste
  function displayPhotographers(list) {
    const container = document.getElementById('photographersList');
    container.innerHTML = ''; // vide le contenu

    if (list.length === 0) {
      container.innerHTML = "<div style='text-align: center; padding: 20px;'><p>Aucun photographe trouvé.</p></div>";
      return;
    }

    list.forEach(p => {
      const div = document.createElement("div");
      div.className = "photographer";

      // Get icon and color based on domain
      const domainIcons = {
        'Portrait': '👤',
        'Mariage': '💒',
        'Nature': '🌿',
        'Mode': '👗',
        'Événementiel': '🎉',
        'Sport': '⚽'
      };

      const domainClasses = {
        'Portrait': 'portrait',
        'Mariage': 'mariage',
        'Nature': 'nature',
        'Mode': 'mode',
        'Événementiel': 'evenementiel',
        'Sport': 'sport'
      };

      const icon = domainIcons[p.domaine] || '📸';
      const iconClass = domainClasses[p.domaine] || 'portrait';

      div.innerHTML = `
        <div class="photographer-icon ${iconClass}">
          ${icon}
        </div>
        <p><strong>${p.nom} ${p.prenom}</strong></p>
        <p style="font-size: 0.9em; color: #666;">${p.domaine}</p>
        <p style="font-size: 0.8em; color: #888;">${p.wilaya}</p>
        <p style="font-size: 0.8em; margin-top: 5px;">${p.bio ? p.bio.substring(0, 50) + '...' : ''}</p>
      `;
      container.appendChild(div);
    });
  }

  // Fonction pour charger les photographes depuis l'API
  async function loadPhotographers(domaine = '', wilaya = '') {
    try {
      const params = new URLSearchParams();
      if (domaine) params.append('domaine', domaine);
      if (wilaya) params.append('wilaya', wilaya);

      const url = `/api/photographers${params.toString() ? '?' + params.toString() : ''}`;
      const response = await fetch(url);

      if (response.ok) {
        const photographers = await response.json();
        allPhotographers = photographers;
        displayPhotographers(photographers);
      } else {
        console.error('Erreur lors du chargement des photographes');
        document.getElementById('photographersList').innerHTML =
          "<div style='text-align: center; padding: 20px;'><p>Erreur lors du chargement des photographes.</p></div>";
      }
    } catch (error) {
      console.error('Erreur:', error);
      document.getElementById('photographersList').innerHTML =
        "<div style='text-align: center; padding: 20px;'><p>Erreur de connexion au serveur.</p></div>";
    }
  }



  // Chargement initial
  loadPhotographers();

  // Événement sur le bouton de filtrage - Multiple event listeners for better compatibility
  document.addEventListener('DOMContentLoaded', function() {
    const filterBtn = document.getElementById('filterBtn');
    const domaineSelect = document.getElementById('domaines');
    const wilayaSelect = document.getElementById('wilaya');

    // Button click event
    if (filterBtn) {
      filterBtn.addEventListener("click", function() {
        console.log('Filter button clicked');
        const domaine = domaineSelect.value;
        const wilaya = wilayaSelect.value;
        console.log('Filtering by:', { domaine, wilaya });
        loadPhotographers(domaine, wilaya);
      });
    }

    // Also add change events to selects for immediate filtering
    if (domaineSelect) {
      domaineSelect.addEventListener("change", function() {
        const domaine = this.value;
        const wilaya = wilayaSelect.value;
        loadPhotographers(domaine, wilaya);
      });
    }

    if (wilayaSelect) {
      wilayaSelect.addEventListener("change", function() {
        const domaine = domaineSelect.value;
        const wilaya = this.value;
        loadPhotographers(domaine, wilaya);
      });
    }
  });

  // Fallback event listener
  document.querySelector(".filters button").addEventListener("click", () => {
    console.log('Fallback filter button clicked');
    const domaine = document.getElementById("domaines").value;
    const wilaya = document.getElementById("wilaya").value;
    loadPhotographers(domaine, wilaya);
  });
</script>


</body>
</html>
