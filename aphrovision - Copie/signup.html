<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Aphrovision - Inscription</title>

  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Open Sans', sans-serif;
      background-color: #faf9f9;
      color: #333;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .top-bar {
      width: 100%;
      padding: 20px 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      background-color: #ffffff;
    }

    .logo {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: bold;
      color: #333;
    }

    .nav-links {
      display: flex;
      gap: 15px;
    }

    .nav-links a {
      color: #333;
      text-decoration: none;
      padding: 8px 16px;
      border: 1px solid #333;
      border-radius: 6px;
      transition: all 0.3s ease;
    }

    .nav-links a:hover {
      background-color: #629208;
      color: white;
      border-color: #629208;
    }

    .main-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
      background-color: #ffffff;
    }

    .content-box {
      background-color: rgb(231, 228, 226);
      display: flex;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
      max-width: 1000px;
      width: 100%;
      flex-wrap: wrap;
    }

    .image-side {
      flex: 1;
      min-width: 300px;
      max-height: 100%;
    }

    .image-side img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .form-container {
      flex: 1;
      padding: 40px;
    }

    h2.title {
      text-align: center;
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      color: #070707;
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-top: 12px;
      font-weight: 600;
      font-size: 0.9em;
    }

    input {
      width: 100%;
      padding: 10px;
      margin-top: 5px;
      border: 1px solid #ccc;
      border-radius: 6px;
      font-size: 0.9em;
    }

    input::placeholder {
      color: #aaa;
    }

    small {
      font-size: 0.75em;
      color: #777;
      display: block;
      margin-top: 4px;
    }

    button {
      margin-top: 20px;
      width: 100%;
      padding: 12px;
      font-size: 1em;
      background-color: #000000;
      color: rgb(255, 255, 255);
      border: none;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    button:hover {
      background-color:#629208;
    }

    p {
      text-align: center;
      margin-top: 15px;
      font-size: 0.85em;
    }

    a {
      color: #629208;
      text-decoration: none;
      font-weight: bold;
    }

    a:hover {
      text-decoration: underline;
    }

    @media screen and (max-width: 800px) {
      .content-box {
        flex-direction: column;
      }

      .form-container {
        padding: 30px 20px;
      }
    }
  </style>
</head>
<body>

  <div class="top-bar">
    <div class="logo">Aphrovision</div>
    <div class="nav-links">
      <a href="login.html">Login</a>
      <a href="signup.html">Signup</a>
    </div>
  </div>

  <div class="main-container">
    <div class="content-box">
      <div class="image-side">
        <img src="photos2.jpg" alt="Aphrovision image">
      </div>

      <div class="form-container">
        <h2 class="title">Créer un compte</h2>
        <div id="error-message" style="color: red; text-align: center; margin-bottom: 10px; display: none;"></div>
        <div id="success-message" style="color: green; text-align: center; margin-bottom: 10px; display: none;"></div>

        <form id="signupForm">
          <label for="user_type">Type de compte</label>
          <select id="user_type" required style="width: 100%; padding: 10px; margin-top: 5px; border: 1px solid #ccc; border-radius: 6px;">
            <option value="">Choisissez votre type de compte</option>
            <option value="client">Client</option>
            <option value="photographe">Photographe</option>
          </select>

          <label for="username">Nom d'utilisateur</label>
          <input type="text" id="username" placeholder="Entrez votre nom d'utilisateur" required>
          <small>Seulement lettres, chiffres et "_", "-", "."</small>

          <label for="firstname">Prénom</label>
          <input type="text" id="firstname" placeholder="Entrez votre prénom" required>

          <label for="lastname">Nom de famille</label>
          <input type="text" id="lastname" placeholder="Entrez votre nom de famille" required>

          <label for="email">Email</label>
          <input type="email" id="email" placeholder="<EMAIL>" required>
          <small>Utilisez une adresse email valide.</small>

          <label for="tel">Numéro de téléphone</label>
          <input type="tel" id="tel" placeholder="+213 123 456 789" required>
          <small>Format recommandé : +213 123 456 789</small>

          <!-- Fields for photographers only -->
          <div id="photographer-fields" style="display: none;">
            <label for="wilaya">Wilaya</label>
            <input type="text" id="wilaya" placeholder="Votre wilaya">

            <label for="domaine">Domaine de spécialité</label>
            <select id="domaine" style="width: 100%; padding: 10px; margin-top: 5px; border: 1px solid #ccc; border-radius: 6px;">
              <option value="">Choisissez votre domaine</option>
              <option value="Portrait">Portrait</option>
              <option value="Mariage">Mariage</option>
              <option value="Événementiel">Événementiel</option>
              <option value="Nature">Nature</option>
              <option value="Architecture">Architecture</option>
              <option value="Mode">Mode</option>
              <option value="Sport">Sport</option>
              <option value="Autre">Autre</option>
            </select>

            <label for="bio">Biographie</label>
            <textarea id="bio" placeholder="Parlez-nous de votre expérience..." style="width: 100%; padding: 10px; margin-top: 5px; border: 1px solid #ccc; border-radius: 6px; min-height: 80px; resize: vertical;"></textarea>
          </div>

          <label for="password">Mot de passe</label>
          <input type="password" id="password" placeholder="******" required>
          <small>Minimum 8 caractères, une majuscule, un chiffre et un symbole.</small>

          <button type="submit">S'inscrire</button>

          <p>Déjà un compte ? <a href="login.html">Se connecter</a></p>
        </form>
      </div>
    </div>
  </div>

  <script>
    // Show/hide photographer fields based on user type
    document.getElementById('user_type').addEventListener('change', function() {
      const photographerFields = document.getElementById('photographer-fields');
      const wilayaField = document.getElementById('wilaya');
      const domaineField = document.getElementById('domaine');
      const bioField = document.getElementById('bio');

      if (this.value === 'photographe') {
        photographerFields.style.display = 'block';
        wilayaField.required = true;
        domaineField.required = true;
        bioField.required = true;
      } else {
        photographerFields.style.display = 'none';
        wilayaField.required = false;
        domaineField.required = false;
        bioField.required = false;
      }
    });

    // Handle form submission
    document.getElementById('signupForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const errorDiv = document.getElementById('error-message');
      const successDiv = document.getElementById('success-message');

      // Hide previous messages
      errorDiv.style.display = 'none';
      successDiv.style.display = 'none';

      // Get form data
      const formData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        password: document.getElementById('password').value,
        user_type: document.getElementById('user_type').value,
        firstname: document.getElementById('firstname').value,
        lastname: document.getElementById('lastname').value,
        phone: document.getElementById('tel').value
      };

      // Add photographer-specific fields if needed
      if (formData.user_type === 'photographe') {
        formData.wilaya = document.getElementById('wilaya').value;
        formData.domaine = document.getElementById('domaine').value;
        formData.bio = document.getElementById('bio').value;
      }

      try {
        // Register the user and create profile in one step
        const registrationData = {
          username: formData.username,
          email: formData.email,
          password: formData.password,
          user_type: formData.user_type,
          nom: formData.lastname,
          prenom: formData.firstname,
          phone: formData.phone
        };

        // Add photographer-specific fields if needed
        if (formData.user_type === 'photographe') {
          registrationData.wilaya = formData.wilaya;
          registrationData.domaine = formData.domaine;
          registrationData.bio = formData.bio;
        }

        const response = await fetch('/api/register-complete', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(registrationData)
        });

        const data = await response.json();

        if (response.ok) {
          successDiv.textContent = 'Compte créé avec succès! Redirection vers la connexion...';
          successDiv.style.display = 'block';

          setTimeout(() => {
            window.location.href = 'login.html';
          }, 2000);
        } else {
          errorDiv.textContent = data.error || 'Erreur lors de la création du compte';
          errorDiv.style.display = 'block';
        }
      } catch (error) {
        console.error('Error:', error);
        errorDiv.textContent = 'Erreur de connexion au serveur';
        errorDiv.style.display = 'block';
      }
    });
  </script>

</body>
</html>
