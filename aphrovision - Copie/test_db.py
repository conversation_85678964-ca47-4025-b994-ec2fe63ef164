#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Test Script for Aphrovision
Run this to test database connectivity and add sample data
"""

import sqlite3
from werkzeug.security import generate_password_hash
from datetime import datetime

def test_database():
    print("Testing database connection...")

    try:
        # Connect to database
        conn = sqlite3.connect('photographe_frelance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Test if tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        print(f"Database connected successfully!")
        print(f"Found {len(tables)} tables:")
        for table in tables:
            print(f"   - {table[0]}")

        # Count records in each table
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   {table_name}: {count} records")

        conn.close()
        return True

    except Exception as e:
        print(f"Database error: {e}")
        return False

def add_sample_data():
    print("\nAdding sample data...")

    try:
        conn = sqlite3.connect('photographe_frelance.db')
        cursor = conn.cursor()

        # Add sample users
        sample_users = [
            ('admin', '<EMAIL>', generate_password_hash('password123'), 'photographe'),
            ('client_test', '<EMAIL>', generate_password_hash('password123'), 'client'),
            ('photo_sophie', '<EMAIL>', generate_password_hash('password123'), 'photographe'),
            ('photo_ahmed', '<EMAIL>', generate_password_hash('password123'), 'photographe'),
            ('photo_fatima', '<EMAIL>', generate_password_hash('password123'), 'photographe'),
        ]

        for username, email, password_hash, user_type in sample_users:
            try:
                cursor.execute('''
                    INSERT INTO users (username, email, password_hash, user_type)
                    VALUES (?, ?, ?, ?)
                ''', (username, email, password_hash, user_type))
                user_id = cursor.lastrowid
                print(f"Created user: {username} (ID: {user_id})")

                # Add corresponding profile
                if user_type == 'client':
                    cursor.execute('''
                        INSERT INTO client (user_id, nom, prenom, email, phone)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (user_id, 'Test', 'Client', email, '0123456789'))
                    print(f"   Created client profile")

                elif user_type == 'photographe':
                    if username == 'admin':
                        cursor.execute('''
                            INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (user_id, 'Admin', 'Super', email, '0987654321', 'Alger', 'Portrait', 'Administrateur du site'))
                    elif username == 'photo_sophie':
                        cursor.execute('''
                            INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (user_id, 'Martin', 'Sophie', email, '0555123456', 'Oran', 'Mariage', 'Photographe de mariage avec 5 ans d\'experience'))
                    elif username == 'photo_ahmed':
                        cursor.execute('''
                            INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (user_id, 'Benali', 'Ahmed', email, '0666789123', 'Constantine', 'Portrait', 'Specialiste en photographie de portrait et corporate'))
                    elif username == 'photo_fatima':
                        cursor.execute('''
                            INSERT INTO photographe (user_id, nom, prenom, email, phone, wilaya, domaine, bio)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (user_id, 'Khelifi', 'Fatima', email, '0777456789', 'Tizi-Ouzou', 'Nature', 'Photographe nature et paysages, passionnee par la beaute de l\'Algerie'))
                    print(f"   Created photographer profile")

            except sqlite3.IntegrityError:
                print(f"User {username} already exists, skipping...")

        conn.commit()
        conn.close()
        print("Sample data added successfully!")

    except Exception as e:
        print(f"Error adding sample data: {e}")

def main():
    print("🧪 Aphrovision Database Test")
    print("=" * 40)

    if test_database():
        add_sample_data()
        print("\n🎉 Database test completed!")
        print("\n📋 Test credentials:")
        print("   Username: admin, Password: password123 (Photographer)")
        print("   Username: client_test, Password: password123 (Client)")
        print("   Username: photo_sophie, Password: password123 (Photographer)")
    else:
        print("\n❌ Database test failed!")

if __name__ == '__main__':
    main()
