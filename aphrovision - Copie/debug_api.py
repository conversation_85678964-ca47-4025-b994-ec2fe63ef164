#!/usr/bin/env python3
"""
Debug script to test the API functionality
"""
import sqlite3
import os

def test_direct_query():
    """Test direct database query"""
    print("🔍 Testing direct database query...")
    
    # Check if database file exists
    db_file = 'photographe_frelance.sqlite'
    if os.path.exists(db_file):
        print(f"✅ Database file '{db_file}' exists")
    else:
        print(f"❌ Database file '{db_file}' not found")
        return
    
    try:
        conn = sqlite3.connect(db_file)
        conn.row_factory = sqlite3.Row
        
        # Test the exact query from the API
        query = "SELECT nom as name, prenom, domaine, wilaya FROM photographe WHERE 1=1"
        cursor = conn.cursor()
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"📊 Found {len(results)} photographers")
        for row in results:
            print(f"   📸 {row['name']} {row['prenom']} - {row['domaine']} ({row['wilaya']})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_with_filter():
    """Test with domain filter"""
    print("\n🎯 Testing with domain filter...")
    
    try:
        conn = sqlite3.connect('photographe_frelance.sqlite')
        conn.row_factory = sqlite3.Row
        
        query = "SELECT nom as name, prenom, domaine, wilaya FROM photographe WHERE domaine = ?"
        cursor = conn.cursor()
        cursor.execute(query, ("Portrait",))
        results = cursor.fetchall()
        
        print(f"📊 Found {len(results)} portrait photographers")
        for row in results:
            print(f"   📸 {row['name']} {row['prenom']} - {row['domaine']} ({row['wilaya']})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

def test_api_import():
    """Test importing the API function"""
    print("\n🔧 Testing API function import...")
    
    try:
        # Import the database manager from our app
        import sys
        sys.path.append('.')
        
        from database_utils import DatabaseManager
        
        db = DatabaseManager()
        query = "SELECT nom as name, prenom, domaine, wilaya FROM photographe WHERE 1=1"
        results = db.execute_query(query)
        
        if results:
            print(f"📊 DatabaseManager found {len(results)} photographers")
            for row in results:
                print(f"   📸 {row['name']} {row['prenom']} - {row['domaine']} ({row['wilaya']})")
        else:
            print("❌ DatabaseManager returned no results")
            
    except Exception as e:
        print(f"❌ Import error: {e}")

if __name__ == "__main__":
    print("🐛 API DEBUG SCRIPT")
    print("=" * 50)
    
    test_direct_query()
    test_with_filter()
    test_api_import()
    
    print("\n" + "=" * 50)
    print("✅ Debug completed!")
