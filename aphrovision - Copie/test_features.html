<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Features - Aphrovision</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            align-items: center;
        }
        select, button, input[type="file"] {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .photographer {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .photographer img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
        }
        .photographer-info {
            flex: 1;
        }
        .name {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        .details {
            margin-top: 5px;
            color: #666;
        }
        .upload-section {
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Filtering & Image Upload</h1>
        
        <div class="filters">
            <label>Domaine:</label>
            <select id="domaines">
                <option value="">Tous les domaines</option>
                <option value="Portrait">Portrait</option>
                <option value="Mariage">Mariage</option>
                <option value="Mode">Mode</option>
                <option value="Nature">Nature</option>
                <option value="Événementiel">Événementiel</option>
                <option value="Sport">Sport</option>
            </select>
            
            <label>Wilaya:</label>
            <select id="wilaya">
                <option value="">Toutes les wilayas</option>
                <option value="Alger">Alger</option>
                <option value="Oran">Oran</option>
                <option value="Constantine">Constantine</option>
                <option value="Annaba">Annaba</option>
                <option value="Tlemcen">Tlemcen</option>
                <option value="Sétif">Sétif</option>
                <option value="Béjaïa">Béjaïa</option>
                <option value="Batna">Batna</option>
                <option value="Ouargla">Ouargla</option>
            </select>
            
            <button onclick="applyFilters()">🔍 Filtrer</button>
            <button onclick="loadAll()">🔄 Tout Afficher</button>
        </div>
        
        <div id="status"></div>
        <div id="results"></div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function displayPhotographers(photographers) {
            const resultsDiv = document.getElementById('results');
            
            if (photographers.length === 0) {
                resultsDiv.innerHTML = '<div class="info">Aucun photographe trouvé avec ces critères.</div>';
                return;
            }

            let html = `<h2>📸 ${photographers.length} Photographe(s) trouvé(s)</h2>`;
            
            photographers.forEach(p => {
                const imageUrl = p.profile_image ? `/uploads/${p.profile_image}` : 'photo2.PNG';
                
                html += `
                    <div class="photographer">
                        <img src="${imageUrl}" alt="${p.nom} ${p.prenom}" onerror="this.src='photo2.PNG'" />
                        <div class="photographer-info">
                            <div class="name">👤 ${p.nom} ${p.prenom}</div>
                            <div class="details">
                                🎯 <strong>Domaine:</strong> ${p.domaine} | 
                                📍 <strong>Wilaya:</strong> ${p.wilaya}
                            </div>
                            <div class="details">
                                📧 ${p.email} | 📱 ${p.phone}
                            </div>
                            <div class="details" style="font-style: italic;">
                                💬 ${p.bio}
                            </div>
                            <div class="upload-section">
                                <input type="file" id="upload-${p.id}" accept="image/*" style="display: none;" onchange="uploadImage(${p.id}, this)">
                                <button onclick="document.getElementById('upload-${p.id}').click()" style="padding: 5px 10px; font-size: 0.9em;">
                                    📷 Upload Photo
                                </button>
                                <span id="upload-status-${p.id}" style="margin-left: 10px; font-size: 0.9em;"></span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        async function loadPhotographers(domaine = '', wilaya = '') {
            try {
                showStatus('🔄 Chargement en cours...', 'info');
                
                const params = new URLSearchParams();
                if (domaine) params.append('domaine', domaine);
                if (wilaya) params.append('wilaya', wilaya);

                const url = `/api/photographers${params.toString() ? '?' + params.toString() : ''}`;
                console.log('Fetching:', url);
                
                const response = await fetch(url);

                if (response.ok) {
                    const photographers = await response.json();
                    console.log('Response:', photographers);
                    
                    showStatus(`✅ Données chargées avec succès! (${photographers.length} photographes)`, 'success');
                    displayPhotographers(photographers);
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('Erreur:', error);
                showStatus(`❌ Erreur: ${error.message}`, 'error');
                document.getElementById('results').innerHTML = '';
            }
        }

        async function uploadImage(photographerId, fileInput) {
            const file = fileInput.files[0];
            if (!file) return;

            const statusSpan = document.getElementById(`upload-status-${photographerId}`);
            statusSpan.innerHTML = '⏳ Uploading...';

            const formData = new FormData();
            formData.append('file', file);

            try {
                const response = await fetch(`/api/photographer/${photographerId}/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    statusSpan.innerHTML = '✅ Uploaded!';
                    statusSpan.style.color = 'green';
                    
                    // Reload photographers to show updated image
                    setTimeout(() => {
                        applyFilters();
                    }, 1000);
                } else {
                    statusSpan.innerHTML = '❌ Failed: ' + result.error;
                    statusSpan.style.color = 'red';
                }
            } catch (error) {
                console.error('Upload error:', error);
                statusSpan.innerHTML = '❌ Error: ' + error.message;
                statusSpan.style.color = 'red';
            }
        }

        function applyFilters() {
            const domaine = document.getElementById('domaines').value;
            const wilaya = document.getElementById('wilaya').value;
            console.log('Applying filters:', { domaine, wilaya });
            loadPhotographers(domaine, wilaya);
        }

        function loadAll() {
            document.getElementById('domaines').value = '';
            document.getElementById('wilaya').value = '';
            loadPhotographers();
        }

        // Auto-filter on select change
        document.getElementById('domaines').addEventListener('change', applyFilters);
        document.getElementById('wilaya').addEventListener('change', applyFilters);

        // Load all photographers on page load
        window.onload = function() {
            showStatus('🚀 Page de test chargée. Les filtres et upload d\'images sont prêts!', 'info');
            loadAll();
        };
    </script>
</body>
</html>
