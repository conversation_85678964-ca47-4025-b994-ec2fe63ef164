#!/usr/bin/env python3
"""
Script to view and query the database data
"""
from database_utils import DatabaseManager, get_all_clients, get_all_photographers, search_photographers

def display_clients():
    """Display all clients"""
    print("👥 CLIENTS")
    print("-" * 50)
    
    clients = get_all_clients()
    if clients:
        for client in clients:
            print(f"ID: {client['id']} | {client['prenom']} {client['nom']}")
            print(f"   📧 {client['email']} | 📱 {client['phone']}")
            print()
    else:
        print("No clients found.")
    print()

def display_photographers():
    """Display all photographers"""
    print("📸 PHOTOGRAPHERS")
    print("-" * 50)
    
    photographers = get_all_photographers()
    if photographers:
        for photographer in photographers:
            print(f"ID: {photographer['id']} | {photographer['prenom']} {photographer['nom']}")
            print(f"   📍 {photographer['wilaya']} | 🎯 {photographer['domaine']}")
            print(f"   📧 {photographer['email']} | 📱 {photographer['phone']}")
            print(f"   💬 {photographer['bio'][:60]}...")
            print()
    else:
        print("No photographers found.")
    print()

def display_projects():
    """Display all projects with client and photographer info"""
    print("🎯 PROJECTS")
    print("-" * 50)
    
    db = DatabaseManager()
    query = """
    SELECT p.id, p.description, p.date_posted, p.image,
           c.prenom as client_prenom, c.nom as client_nom,
           ph.prenom as photo_prenom, ph.nom as photo_nom, ph.domaine
    FROM projet p
    JOIN client c ON p.id_client = c.id
    JOIN photographe ph ON p.id_photographe = ph.id
    ORDER BY p.date_posted DESC
    """
    
    projects = db.execute_query(query)
    if projects:
        for project in projects:
            print(f"ID: {project['id']} | 📅 {project['date_posted']}")
            print(f"   👤 Client: {project['client_prenom']} {project['client_nom']}")
            print(f"   📸 Photographer: {project['photo_prenom']} {project['photo_nom']} ({project['domaine']})")
            print(f"   📝 {project['description']}")
            print(f"   🖼️ Image: {project['image']}")
            print()
    else:
        print("No projects found.")
    print()

def display_photos():
    """Display photos with photographer info"""
    print("🖼️ PHOTOS")
    print("-" * 50)
    
    db = DatabaseManager()
    query = """
    SELECT ph.id, ph.image, ph.description,
           p.prenom as photographer_prenom, p.nom as photographer_nom, p.domaine
    FROM photo ph
    JOIN photographe p ON ph.id_photographe = p.id
    ORDER BY ph.id
    """
    
    photos = db.execute_query(query)
    if photos:
        for photo in photos:
            print(f"ID: {photo['id']} | 🖼️ {photo['image']}")
            print(f"   📸 By: {photo['photographer_prenom']} {photo['photographer_nom']} ({photo['domaine']})")
            print(f"   📝 {photo['description']}")
            print()
    else:
        print("No photos found.")
    print()

def display_announcements():
    """Display all announcements"""
    print("📢 ANNOUNCEMENTS")
    print("-" * 50)
    
    db = DatabaseManager()
    announcements = db.execute_query("SELECT * FROM annonce ORDER BY date_posted DESC")
    
    if announcements:
        for announcement in announcements:
            status_emoji = "✅" if announcement['etet'] == "Disponible" else "🔒"
            print(f"ID: {announcement['id']} | {status_emoji} {announcement['etet']}")
            print(f"   🏷️ {announcement['categore']} | 💰 {announcement['prix']} DA")
            print(f"   📋 {announcement['titre']}")
            print(f"   📝 {announcement['description'][:80]}...")
            print(f"   📅 {announcement['date_posted']} | 🖼️ {announcement['image']}")
            print()
    else:
        print("No announcements found.")
    print()

def search_demo():
    """Demonstrate search functionality"""
    print("🔍 SEARCH DEMONSTRATIONS")
    print("-" * 50)
    
    # Search photographers by domain
    print("🎯 Photographers specializing in 'Mariage':")
    wedding_photographers = search_photographers(domaine="Mariage")
    if wedding_photographers:
        for photographer in wedding_photographers:
            print(f"   📸 {photographer['prenom']} {photographer['nom']} - {photographer['wilaya']}")
    print()
    
    # Search photographers by wilaya
    print("📍 Photographers in 'Alger':")
    alger_photographers = search_photographers(wilaya="Alger")
    if alger_photographers:
        for photographer in alger_photographers:
            print(f"   📸 {photographer['prenom']} {photographer['nom']} - {photographer['domaine']}")
    print()
    
    # Search by both criteria
    print("🎯📍 Portrait photographers in 'Alger':")
    specific_photographers = search_photographers(domaine="Portrait", wilaya="Alger")
    if specific_photographers:
        for photographer in specific_photographers:
            print(f"   📸 {photographer['prenom']} {photographer['nom']}")
    else:
        print("   No photographers found matching these criteria.")
    print()

def display_statistics():
    """Display database statistics"""
    print("📊 DATABASE STATISTICS")
    print("-" * 50)
    
    from database_utils import get_database_stats
    stats = get_database_stats()
    
    for table, count in stats.items():
        print(f"{table.capitalize()}: {count} records")
    
    # Additional statistics
    db = DatabaseManager()
    
    # Photographers by domain
    print("\n📸 Photographers by Domain:")
    domains = db.execute_query("SELECT domaine, COUNT(*) as count FROM photographe GROUP BY domaine ORDER BY count DESC")
    if domains:
        for domain in domains:
            print(f"   {domain['domaine']}: {domain['count']}")
    
    # Photographers by wilaya
    print("\n📍 Photographers by Wilaya:")
    wilayas = db.execute_query("SELECT wilaya, COUNT(*) as count FROM photographe GROUP BY wilaya ORDER BY count DESC")
    if wilayas:
        for wilaya in wilayas:
            print(f"   {wilaya['wilaya']}: {wilaya['count']}")
    
    # Announcements by category
    print("\n📢 Announcements by Category:")
    categories = db.execute_query("SELECT categore, COUNT(*) as count FROM annonce GROUP BY categore ORDER BY count DESC")
    if categories:
        for category in categories:
            print(f"   {category['categore']}: {category['count']}")
    
    print()

def main():
    """Main function to display all data"""
    print("🗄️ DATABASE CONTENT VIEWER")
    print("=" * 60)
    
    display_statistics()
    display_clients()
    display_photographers()
    display_projects()
    display_photos()
    display_announcements()
    search_demo()
    
    print("=" * 60)
    print("✅ Data viewing completed!")

if __name__ == "__main__":
    main()
